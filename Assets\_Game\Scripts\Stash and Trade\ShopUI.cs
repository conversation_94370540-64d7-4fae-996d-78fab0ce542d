using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

public class ShopUI : MonoBehaviour
{
    [Header("Required References")]
    [SerializeField] private ShopSystem shopSystem;
    [SerializeField] private EquipmentManager equipmentManager;
    [SerializeField] private InvDragAndDropManager dragAndDropManager;
    [SerializeField] private UIDocument uiDocument;
    [SerializeField] private Transform itemSpawnPoint; // Where purchased items appear in world
    [SerializeField] private InvItemDropping itemDropping; // Reference to the item dropping system
    [SerializeField] private MoneyCounterAnimation moneyCounterAnimation;

    private VisualElement root;
    private ScrollView shopScrollView;
    private VisualElement shopGrid;
    private Label totalValueLabel;
    private Button buyButton;
    private UIPanelManager uiPanelManager;
    private GridRenderer gridRenderer;
    private Label traderNameLabel;

    // Selected items tracking
    private Dictionary<string, ItemSelection> selectedItems = new Dictionary<string, ItemSelection>();
    private int totalSelectionValue = 0;

    // Sell mode UI elements
    private VisualElement buyModeContainer;
    private VisualElement sellModeContainer;
    private Button buyModeButton;
    private Button sellModeButton;
    private VisualElement sellGrid;
    private Label sellItemCountLabel;
    private Label sellTotalValueLabel;
    private Button sellButton;
    
    // Track sell mode state
    private bool isInSellMode = false;

    private float lastUIUpdateTime = 0f;
    private float uiUpdateInterval = 0.25f; // Update UI 4 times per second
    
    // Reference to the bottom instruction text
    private Label instructionLabel;
    // Reference to the sell mode instruction container
    private VisualElement sellInstructionsContainer;

    // Add a flag to track if we just completed a purchase
    private bool justCompletedPurchase = false;

    // Add a flag to prevent redundant currency updates
    private bool isUpdatingCurrency = false;
    private float lastCurrencyUpdateTime = 0f;
    private const float CURRENCY_UPDATE_THROTTLE = 0.1f; // 10 updates per second max

    // Class to track selected items
    private class ItemSelection
    {
        public Item Item { get; set; }
        public int Quantity { get; set; }
        public VisualElement Element { get; set; }
    }

    private void Start()
    {
        // Find UIPanelManager for coordination
        uiPanelManager = FindObjectOfType<UIPanelManager>();
        
        StartCoroutine(DelayedInitialization());
    }
    
    private IEnumerator DelayedInitialization()
    {
        // Wait until game has fully initialized
        yield return new WaitForSeconds(0.2f);
        
        // Try to find dependencies if not set
        if (shopSystem == null) shopSystem = FindObjectOfType<ShopSystem>();
        if (equipmentManager == null) equipmentManager = FindObjectOfType<EquipmentManager>();
        if (dragAndDropManager == null) dragAndDropManager = FindObjectOfType<InvDragAndDropManager>();
        if (itemDropping == null) itemDropping = FindObjectOfType<InvItemDropping>();
        if (moneyCounterAnimation == null) moneyCounterAnimation = FindObjectOfType<MoneyCounterAnimation>();
        
        // Initialize UI
        if (ValidateReferences())
        {
            InitializeComponents();
            RegisterUICallbacks();
            PopulateShopItems();
            
            // Subscribe to currency change events from CurrencyManager
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.Instance.OnCurrencyChanged += (oldValue, newValue) => {
                    // Only animate if the UI is visible and initialized
                    if (root != null && root.style.display != DisplayStyle.None)
                    {
                        UpdateCurrencyDisplay(newValue);
                    }
                    if (!isInSellMode) UpdateItemPriceColors();
                    Debug.Log($"ShopUI: Currency updated to {newValue} via CurrencyManager event");
                };
                
                // Force initial update with current currency value using proper formatting
                int currentCurrency = CurrencyManager.Instance.CurrentCurrency;
                
                // Find all currency labels and apply proper formatting
                if (root != null)
                {
                    var currencyLabels = root.Query<Label>().Where(l => 
                        l.text != null && 
                        l.text.Contains("€") && 
                        l != sellTotalValueLabel && 
                        l != totalValueLabel &&
                        !l.ClassListContains("shop-item-price")).ToList();
                    
                    foreach (var label in currencyLabels)
                    {
                        if (moneyCounterAnimation != null)
                        {
                            // Set initial value with proper coloring without animation
                            moneyCounterAnimation.SetColoredCounterValue(label, currentCurrency);
                        }
                        else
                        {
                            label.text = $"€ {currentCurrency:000000000}";
                        }
                    }
                }
            }
            // If CurrencyManager doesn't exist, create one to ensure the system works
            else
            {
                Debug.LogError("ShopUI: CurrencyManager not found! Creating one to ensure currency system works.");
                GameObject cmObj = new GameObject("CurrencyManager");
                CurrencyManager currencyManager = cmObj.AddComponent<CurrencyManager>();
                
                currencyManager.OnCurrencyChanged += (oldValue, newValue) => {
                    if (root != null && root.style.display != DisplayStyle.None)
                    {
                        UpdateCurrencyDisplay(newValue);
                    }
                    if (!isInSellMode) UpdateItemPriceColors();
                    Debug.Log($"ShopUI: Currency updated to {newValue} via CurrencyManager event");
                };
                
                // Initial currency update with proper formatting
                int currentCurrency = currencyManager.CurrentCurrency;
                if (root != null && moneyCounterAnimation != null)
                {
                    var currencyLabels = root.Query<Label>().Where(l => 
                        l.text != null && 
                        l.text.Contains("€") && 
                        l != sellTotalValueLabel && 
                        l != totalValueLabel &&
                        !l.ClassListContains("shop-item-price")).ToList();
                    
                    foreach (var label in currencyLabels)
                    {
                        moneyCounterAnimation.SetColoredCounterValue(label, currentCurrency);
                    }
                }
            }
            
            // Update UI elements that depend on currency
            UpdateItemPriceColors();
        }
    }

    private void PopulateShopItems()
    {
        if (shopGrid == null || shopSystem == null) return;
        
        shopGrid.Clear();
        
        // Get the active trader for styling and grid dimensions
        TraderInventory trader = shopSystem.GetActiveTrader();

        // Get items from shop system
        var availableItems = shopSystem.GetAvailableItems();
        
        // If no items, add a message
        if (availableItems.Count == 0)
        {
            var noItemsLabel = new Label("No items available");
            noItemsLabel.AddToClassList("no-items-message");
            shopGrid.Add(noItemsLabel);
            return;
        }
        
        // Create the grid layout
        int gridWidth = trader != null ? trader.gridWidth : 6;
        int gridHeight = trader != null ? trader.gridHeight : 5;
        float gridSize = 64f;
        
        // Create a proper grid container
        var gridContainer = new VisualElement();
        gridContainer.AddToClassList("shop-grid-container");
        gridContainer.style.width = gridWidth * gridSize;
        gridContainer.style.height = gridHeight * gridSize;
        
        // Create a 2D grid for occupancy tracking
        bool[,] occupiedCells = new bool[gridWidth, gridHeight];
        
        // Sort items by size (larger first)
        availableItems.Sort((a, b) => {
            int sizeA = a.SlotDimension.Width * a.SlotDimension.Height;
            int sizeB = b.SlotDimension.Width * b.SlotDimension.Height;
            return sizeB.CompareTo(sizeA); // Descending order
        });
        
        // Place items in the grid
        foreach (var item in availableItems)
        {
            // Find a place for this item
            bool placed = false;
            
            for (int y = 0; y < gridHeight && !placed; y++)
            {
                for (int x = 0; x < gridWidth && !placed; x++)
                {
                    if (CanPlaceItemAt(item, x, y, occupiedCells, gridWidth, gridHeight))
                    {
                        // Place the item here
                        PlaceItemAt(item, x, y, occupiedCells, gridContainer, gridSize);
                        placed = true;
                    }
                }
            }
            
            if (!placed)
            {
                Debug.LogWarning($"Could not fit item {item.itemName} in the shop grid");
            }
        }
        
        // Add the grid to the shop
        shopGrid.Add(gridContainer);
    }

    private bool CanPlaceItemAt(Item item, int x, int y, bool[,] occupiedCells, int gridWidth, int gridHeight)
    {
        int width = item.SlotDimension.Width;
        int height = item.SlotDimension.Height;
        
        // Check if item fits within grid bounds
        if (x + width > gridWidth || y + height > gridHeight)
            return false;
        
        // Check if any cell is already occupied
        for (int dy = 0; dy < height; dy++)
        {
            for (int dx = 0; dx < width; dx++)
            {
                if (occupiedCells[x + dx, y + dy])
                    return false;
            }
        }
        
        return true;
    }

    private void PlaceItemAt(Item item, int x, int y, bool[,] occupiedCells, VisualElement gridContainer, float gridSize)
    {
        int width = item.SlotDimension.Width;
        int height = item.SlotDimension.Height;
        
        // Mark cells as occupied
        for (int dy = 0; dy < height; dy++)
        {
            for (int dx = 0; dx < width; dx++)
            {
                occupiedCells[x + dx, y + dy] = true;
            }
        }
        
        // Create item visual element
        var itemElement = new VisualElement();
        itemElement.AddToClassList("shop-item");
        
        // Generate a unique identifier for this item instance in the shop
        string uniqueId = $"{item.itemName}_{System.Guid.NewGuid().ToString().Substring(0, 8)}";
        
        // Store both the item reference and the unique identifier
        itemElement.userData = uniqueId;
        
        // Position based on grid (needs to be inline since it's dynamic)
        itemElement.style.left = x * gridSize;
        itemElement.style.top = y * gridSize;
        itemElement.style.width = width * gridSize;
        itemElement.style.height = height * gridSize;
        
        // Add item icon if available
        if (item.Icon != null)
        {
            var iconImage = new Image();
            iconImage.sprite = item.Icon;
            iconImage.AddToClassList("shop-item-icon");
            itemElement.Add(iconImage);
        }

        // Get the correct price from the ShopSystem, which will check the trader's custom pricing
        int itemPrice = shopSystem != null ? shopSystem.GetItemPrice(item) : item.Price;
        
        // Show price - use € symbol and actual price (not currency balance)
        var priceLabel = new Label($"€ {itemPrice}");
        priceLabel.AddToClassList("shop-item-price");
        
        // Change color based on whether player has enough money
        bool canAfford = shopSystem != null && shopSystem.HasEnoughMoney(itemPrice);
        if (!canAfford)
        {
            priceLabel.AddToClassList("price-cannot-afford");
        }
        
        itemElement.Add(priceLabel);
        
        // Register click event - pass both the element and the item
        itemElement.RegisterCallback<ClickEvent>(evt => OnShopItemClicked(itemElement, item));
        
        // Add to grid
        gridContainer.Add(itemElement);
    }

    private void OnShopItemClicked(VisualElement itemElement, Item item)
    {
        if (itemElement == null || item == null) return;

        // Get the unique identifier for this item instance
        string uniqueId = itemElement.userData.ToString();

        // Toggle selection
        if (selectedItems.ContainsKey(uniqueId))
        {
            // Deselect
            itemElement.RemoveFromClassList("shop-item--selected");
            selectedItems.Remove(uniqueId);
            totalSelectionValue -= shopSystem.GetItemPrice(item);
        }
        else
        {
            // Select
            itemElement.AddToClassList("shop-item--selected");
            
            // Create new selection
            var selection = new ItemSelection
            {
                Item = item,
                Quantity = 1,
                Element = itemElement
            };
            
            selectedItems[uniqueId] = selection;
            totalSelectionValue += shopSystem.GetItemPrice(item);
        }
        
        // Update UI
        UpdateSelectedItemsTotals();
    }

    private void UpdateSelectedItemsTotals()
    {
        totalSelectionValue = 0;
        
        foreach (var selection in selectedItems.Values)
        {
            totalSelectionValue += shopSystem.GetItemPrice(selection.Item);
        }
        
        // Update UI
        if (totalValueLabel != null)
        {
            // Use the colored counter if available
            if (moneyCounterAnimation != null && totalSelectionValue > 0)
            {
                // For total value label, we only need to color the numbers, not use the full format
                string formattedValue = totalSelectionValue.ToString();
                totalValueLabel.enableRichText = true;
                
                // Create rich text with different colors
                System.Text.StringBuilder richText = new System.Text.StringBuilder();
                richText.Append("<color=#464646>€</color> ");
                
                foreach (char digit in formattedValue)
                {
                    if (digit == '0')
                    {
                        // Use gray color for zeros
                        richText.Append("<color=#464646>0</color>");
                    }
                    else
                    {
                        // Use light gray color for non-zero digits
                        richText.Append("<color=#969696>" + digit + "</color>");
                    }
                }
                
                totalValueLabel.text = richText.ToString();
            }
            else
            {
                // Fallback to regular formatting with proper color handling
                if (totalSelectionValue > 0)
                {
                    totalValueLabel.text = $"€ {totalSelectionValue}";
                    totalValueLabel.style.color = Color.white;
                }
                else
                {
                    totalValueLabel.text = "€ 0";
                    // Set gray color when value is 0 (same as sell mode)
                    totalValueLabel.style.color = new Color(0.6f, 0.6f, 0.6f);
                }
            }
            
            // IMPORTANT: Set color based on whether there are items AND affordability (match sell mode logic)
            bool canAffordTotal = shopSystem != null && shopSystem.HasEnoughMoney(totalSelectionValue);
            
            if (totalSelectionValue == 0)
            {
                // When no value, use gray color (same as sell mode)
                totalValueLabel.style.color = new Color(0.6f, 0.6f, 0.6f);
            }
            else
            {
                // When there's value, check affordability
                totalValueLabel.style.color = canAffordTotal ? Color.white : new Color(0.6f, 0.6f, 0.6f);
            }
        }
        
        // Enable/disable buy button and update text
        if (buyButton != null)
        {
            bool canAffordTotal = shopSystem != null && shopSystem.HasEnoughMoney(totalSelectionValue);
            
            // Always keep text as "BUY" instead of changing to "Close"
            buyButton.text = "BUY";
            
            // Only enable the button if items are selected AND player can afford them
            buyButton.SetEnabled(selectedItems.Count > 0 && canAffordTotal);
            
            // Apply the disabled class manually to ensure styling works properly
            if (selectedItems.Count == 0 || !canAffordTotal)
            {
                buyButton.AddToClassList("disabled");
            }
            else
            {
                buyButton.RemoveFromClassList("disabled");
            }
        }
    }

    public void UpdateUI()
    {
        if (isInSellMode)
        {
            InitializeSellGrid();
            UpdateSellInfoLabels();
        }
        else
        {
            PopulateShopItems();
            UpdateSelectedItemsTotals();
            
            // Update price colors based on player's current balance
            UpdateItemPriceColors();
        }
        
        // Get current currency to ensure consistent display
        int currentCurrency = CurrencyManager.Instance != null ? CurrencyManager.Instance.CurrentCurrency : 0;
        
        // Ensure all currency labels are in sync
        EnsureCurrencyLabelsSynchronized(currentCurrency);
    }
    
    private bool ValidateReferences()
    {
        if (shopSystem == null)
        {
            Debug.LogError("ShopSystem reference not set in inspector", this);
            return false;
        }
        if (equipmentManager == null)
        {
            Debug.LogError("EquipmentManager reference not set in inspector", this);
            return false;
        }
        if (dragAndDropManager == null)
        {
            Debug.LogError("InvDragAndDropManager reference not set in inspector", this);
            return false;
        }
        if (uiDocument == null)
        {
            Debug.LogError("UIDocument reference not set in inspector", this);
            return false;
        }
        if (itemSpawnPoint == null)
        {
            Debug.LogWarning("Item spawn point not set - will use player position instead", this);
        }
        return true;
    }

    private void InitializeComponents()
    {
        if (!ValidateReferences()) return;

        root = uiDocument.rootVisualElement;

        // Create or get GridRenderer
        gridRenderer = GetComponent<GridRenderer>();
        if (gridRenderer == null)
        {
            gridRenderer = gameObject.AddComponent<GridRenderer>();
            gridRenderer.Initialize(uiDocument, equipmentManager, dragAndDropManager);
        }

        // Buy mode elements
        buyModeContainer = root.Q<VisualElement>("BuyModeContainer");
        shopScrollView = root.Q<ScrollView>("ShopScrollView");
        shopGrid = root.Q<VisualElement>("ShopGrid");
        totalValueLabel = root.Q<Label>("BuyTotalValue");
        buyButton = root.Q<Button>("BuyButton");

        // Sell mode elements
        sellModeContainer = root.Q<VisualElement>("SellModeContainer");
        sellGrid = root.Q<VisualElement>("SellGrid");
        sellTotalValueLabel = root.Q<Label>("SellTotalValue");
        sellButton = root.Q<Button>("SellButton");
        
        // Get references to the instruction elements
        instructionLabel = root.Q<Label>(className: "instruction-text");
        
        // Mode switch buttons
        buyModeButton = root.Q<Button>("BuyModeButton");
        sellModeButton = root.Q<Button>("SellModeButton");

        // Initialize both total value labels with consistent gray color for 0 values
        if (totalValueLabel != null)
        {
            totalValueLabel.text = "€ 0";
            totalValueLabel.style.color = new Color(0.6f, 0.6f, 0.6f); // Gray for 0 value
        }
        
        if (sellTotalValueLabel != null)
        {
            sellTotalValueLabel.text = "€ 0";
            sellTotalValueLabel.style.color = new Color(0.6f, 0.6f, 0.6f); // Gray for 0 value
        }

        if (buyButton != null)
        {
            buyButton.clicked += OnBuyButtonClicked;
            // Initialize in disabled state
            buyButton.SetEnabled(false);
            buyButton.AddToClassList("disabled");
        }
        
        if (sellButton != null)
        {
            sellButton.clicked += OnSellButtonClicked;
            // Initialize in disabled state
            sellButton.SetEnabled(false);
            sellButton.AddToClassList("disabled");
        }
        
        // Start container change monitoring
        StartMonitoringSellContainer();
    }

    private void RegisterUICallbacks()
    {
        if (root == null) return;

        var buyButton = root.Q<Button>("BuyButton");
        if (buyButton != null)
        {
            buyButton.clicked += OnBuyButtonClicked;
        }
        
        var sellButton = root.Q<Button>("SellButton");
        if (sellButton != null)
        {
            sellButton.clicked += OnSellButtonClicked;
        }
        
        // Mode switch buttons
        var buyModeButton = root.Q<Button>("BuyModeButton");
        if (buyModeButton != null)
        {
            buyModeButton.clicked += () => SetShopMode(false);
        }
        
        var sellModeButton = root.Q<Button>("SellModeButton");
        if (sellModeButton != null)
        {
            sellModeButton.clicked += () => SetShopMode(true);
        }
    }

    private void UpdateInfoLabels()
    {
        if (isInSellMode)
        {
            UpdateSellInfoLabels();
        }
        else
        {
            UpdateSelectedItemsTotals();
        }
    }
    
    private void UpdateSellInfoLabels()
    {
        var sellContainer = shopSystem.GetSellContainer();
        if (sellContainer == null) return;
        
        int totalValue = 0;
        float totalWeight = 0f;
        int itemCount = 0;

        foreach (var stack in sellContainer.GetItems())
        {
            if (stack?.Item != null)
            {
                itemCount += stack.Quantity;
                totalWeight += stack.Item.Weight * stack.Quantity;
                
                // Use GetSellValueForItem for consistent pricing
                int itemValue = shopSystem.GetSellValueForItem(stack.Item, stack.Quantity);
                Debug.Log($"UpdateSellInfoLabels: Added value {itemValue} for {stack.Quantity}x {stack.Item.itemName}");
                totalValue += itemValue;
            }
        }
        
        if (sellTotalValueLabel != null)
        {
            // Use the colored counter if available
            if (moneyCounterAnimation != null && totalValue > 0)
            {
                // For sell value label, we only need to color the numbers, not use the full format
                string formattedValue = totalValue.ToString();
                sellTotalValueLabel.enableRichText = true;
                
                // Create rich text with different colors
                System.Text.StringBuilder richText = new System.Text.StringBuilder();
                richText.Append("<color=#464646>€</color> ");
                
                foreach (char digit in formattedValue)
                {
                    if (digit == '0')
                    {
                        // Use gray color for zeros
                        richText.Append("<color=#464646>0</color>");
                    }
                    else
                    {
                        // Use light gray color for non-zero digits
                        richText.Append("<color=#969696>" + digit + "</color>");
                    }
                }
                
                sellTotalValueLabel.text = richText.ToString();
            }
            else
            {
                // Fallback to regular formatting with proper color handling
                if (totalValue > 0)
                {
                    sellTotalValueLabel.text = $"€ {totalValue}";
                    sellTotalValueLabel.style.color = Color.white;
                }
                else
                {
                    sellTotalValueLabel.text = "€ 0";
                    // Set the same gray color as buy mode when value is 0
                    sellTotalValueLabel.style.color = new Color(0.6f, 0.6f, 0.6f);
                }
            }
            
            // IMPORTANT: Set color based on whether there are items (match buy mode logic)
            if (totalValue == 0)
            {
                // When no value, use gray color (same as buy mode)
                sellTotalValueLabel.style.color = new Color(0.6f, 0.6f, 0.6f);
            }
            else
            {
                // When there's value, use white color (same as buy mode)
                sellTotalValueLabel.style.color = Color.white;
            }
        }
            
        if (sellButton != null)
        {
            sellButton.text = "SELL";
            sellButton.SetEnabled(itemCount > 0);
            
            // Apply the disabled class manually to ensure styling works properly
            if (itemCount == 0)
            {
                sellButton.AddToClassList("disabled");
            }
            else
            {
                sellButton.RemoveFromClassList("disabled");
            }
            
            // Match the text color style to the buy button
            if (itemCount > 0) {
                sellButton.style.color = Color.white;
            } else {
                sellButton.style.color = new Color(0.6f, 0.6f, 0.6f);
            }
        }
    }

    private void OnBuyButtonClicked()
    {
        // If no items are selected, don't do anything
        if (selectedItems.Count == 0)
        {
            return;
        }

        // Reset the flag since we're starting a new purchase
        justCompletedPurchase = false;

        // Calculate total purchase cost upfront
        int totalPurchaseCost = 0;
        bool canAffordAll = true;
        
        // Verify we can afford everything first
        foreach (var selection in selectedItems.Values)
        {
            int itemPrice = shopSystem.GetItemPrice(selection.Item);
            int cost = itemPrice * selection.Quantity;
            totalPurchaseCost += cost;
        }
        
        canAffordAll = shopSystem.HasEnoughMoney(totalPurchaseCost);
        
        if (!canAffordAll)
        {
            Debug.LogWarning($"Not enough money to purchase all selected items. Required: {totalPurchaseCost}");
            // Update the UI to show updated affordability
            UpdateItemPriceColors();
            UpdateSelectedItemsTotals();
            return;
        }
        
        // Remember the currency before transaction
        int beforeBuyCurrency = CurrencyManager.Instance != null ? CurrencyManager.Instance.CurrentCurrency : 0;
        
        // Try to purchase all selected items
        bool allPurchased = true;
        List<string> purchasedItems = new List<string>(); // Track items to remove from selection
        
        // First check if all items can be added to inventory
        bool canAddAllToInventory = true;
        foreach (var selection in selectedItems.Values)
        {
            if (!shopSystem.CanAddItemToInventory(selection.Item, selection.Quantity))
            {
                canAddAllToInventory = false;
                Debug.LogWarning($"Not enough inventory space for {selection.Item.itemName}");
                break;
            }
        }
        
        if (!canAddAllToInventory)
        {
            // Don't proceed if we can't add all items
            return;
        }
        
        // We've verified we can afford all items and have space - now make a single currency deduction
        if (totalPurchaseCost > 0)
        {
            shopSystem.DeductMoney(totalPurchaseCost);
        }
        
        // Now add all items to inventory
        foreach (var keyValuePair in selectedItems)
        {
            string uniqueId = keyValuePair.Key;
            var selection = keyValuePair.Value;
            
            // Add item directly to inventory (without deducting money again)
            // We pass false for reduceStock because we'll handle stock reduction manually for each item instance
            bool added = shopSystem.AddItemToPlayerInventory(selection.Item, selection.Quantity);
            
            if (added)
            {
                purchasedItems.Add(uniqueId);
                selection.Element.RemoveFromClassList("shop-item--selected");
                
                // Reduce trader's stock quantity for non-unlimited items
                if (shopSystem.GetActiveTrader() != null && !IsUnlimitedItem(selection.Item))
                {
                    shopSystem.GetActiveTrader().ReduceItemQuantity(selection.Item, selection.Quantity);

                    // Persist trader state after stock change
                    TraderPersistenceService.SaveState(shopSystem.GetActiveTrader());
                    
                    // Hide this specific item element
                    selection.Element.style.display = DisplayStyle.None;
                }
            }
            else
            {
                allPurchased = false;
                Debug.LogError($"Failed to add {selection.Item.itemName} to inventory even after space check");
            }
        }
        
        // Set the flag if any items were purchased successfully
        if (purchasedItems.Count > 0)
        {
            justCompletedPurchase = true;
            
            // Log the successful purchase
            Debug.Log($"Successfully purchased {purchasedItems.Count} different items for a total of {totalPurchaseCost}");
            
            // Force refresh of inventory UI to show newly purchased items
            var invUI = FindObjectOfType<InvUI>();
            if (invUI != null)
            {
                invUI.UpdateUI();
                Debug.Log("Forced inventory UI update after purchase");
            }
        }
        
        // Remove purchased items from selection
        foreach (var uniqueId in purchasedItems)
        {
            selectedItems.Remove(uniqueId);
        }
        
        // Get currency after transaction
        int afterBuyCurrency = CurrencyManager.Instance != null ? CurrencyManager.Instance.CurrentCurrency : 0;
        
        // Log the currency change
        Debug.Log($"OnBuyButtonClicked: Currency changed from {beforeBuyCurrency} to {afterBuyCurrency} (expected change: -{totalPurchaseCost})");
        
        // Make sure all currency labels are synchronized
        EnsureCurrencyLabelsSynchronized(afterBuyCurrency);
        
        // Update UI - including currency display
        UpdateSelectedItemsTotals();
        
        // Update item price colors based on new currency amount
        UpdateItemPriceColors();
        
        // We don't need to refresh shop items since we've already hidden the purchased ones
        // This improves performance by avoiding unnecessary redraws
    }
    
    // Helper method to check if an item has unlimited quantity
    private bool IsUnlimitedItem(Item item)
    {
        if (shopSystem == null || shopSystem.GetActiveTrader() == null) return false;
        
        var stockItems = shopSystem.GetActiveTrader().GetStockItems();
        foreach (var stockItem in stockItems)
        {
            if (stockItem.item == item && stockItem.unlimited)
            {
                return true;
            }
        }
        
        return false;
    }
    
    private void SpawnPurchasedItemInWorld(Item item, int quantity)
    {
        if (item == null) return;

        // Determine spawn position - use itemSpawnPoint if available, otherwise use a position in front of the player
        Vector3 spawnPosition;
        if (itemSpawnPoint != null)
        {
            spawnPosition = itemSpawnPoint.position;
        }
        else
        {
            // Default to position in front of player
            var player = Camera.main.transform;
            spawnPosition = player.position + player.forward * 2f;
        }
        
        // Use InvItemDropping to spawn the item
        if (itemDropping != null)
        {
            GameObject droppedItem = itemDropping.DropItem(item, quantity);
            if (droppedItem != null)
            {
                Debug.Log($"Successfully spawned {quantity}x {item.itemName} at {spawnPosition}");
            }
            else
            {
                Debug.LogError($"Failed to spawn {quantity}x {item.itemName}");
            }
        }
        else
        {
            Debug.LogError("InvItemDropping reference not set in ShopUI");
        }
    }
    
    private void ClearSelections()
    {
        // Remove selected class from all elements
        foreach (var selection in selectedItems.Values)
        {
            if (selection.Element != null)
            {
                selection.Element.RemoveFromClassList("shop-item--selected");
            }
        }
        
        // Clear dictionary
        selectedItems.Clear();
        totalSelectionValue = 0;
    }
    
    private void OnSellButtonClicked()
    {
        var sellContainer = shopSystem.GetSellContainer();
        if (sellContainer == null || shopSystem == null) return;
        
        // Calculate total value before selling for logging purposes
        int totalSaleValue = 0;
        foreach (var stack in sellContainer.GetItems())
        {
            if (stack?.Item != null)
            {
                totalSaleValue += shopSystem.GetSellValueForItem(stack.Item, stack.Quantity);
            }
        }
        
        // Debug log to show all items in the sell container before selling
        Debug.Log($"OnSellButtonClicked: Sell container has {sellContainer.GetItems().Count} item stacks");
        
        // Remember the currency before selling
        int beforeSellCurrency = CurrencyManager.Instance != null ? CurrencyManager.Instance.CurrentCurrency : 0;
        
        // IMPORTANT: Use the ShopSystem's ConfirmSale method instead of duplicating the logic
        shopSystem.ConfirmSale();
        
        // Get currency after selling
        int afterSellCurrency = CurrencyManager.Instance != null ? CurrencyManager.Instance.CurrentCurrency : 0;
        
        // Log the currency change
        Debug.Log($"OnSellButtonClicked: Currency changed from {beforeSellCurrency} to {afterSellCurrency} (expected change: +{totalSaleValue})");
        
        // Make sure all currency labels are synchronized
        EnsureCurrencyLabelsSynchronized(afterSellCurrency);
        
        // Update UI after the sale
        UpdateUI();
    }
    
    private void EnsureCurrencyLabelsSynchronized(int currencyValue)
    {
        if (root == null) return;
        
        var currencyLabels = root.Query<Label>().Where(l => 
            l.text != null && 
            l.text.Contains("€") && 
            l != sellTotalValueLabel && 
            l != totalValueLabel &&
            !l.ClassListContains("shop-item-price")).ToList();
            
        foreach (var label in currencyLabels)
        {
            int displayedValue = ExtractCurrentValue(label.text);
            if (displayedValue != currencyValue)
            {
                Debug.Log($"EnsureCurrencyLabelsSynchronized: Correcting label from {displayedValue} to {currencyValue}");
                if (moneyCounterAnimation != null)
                {
                    // Always use SetColoredCounterValue for non-animated updates during sync
                    moneyCounterAnimation.SetColoredCounterValue(label, currencyValue);
                }
                else
                {
                    label.text = $"€ {currencyValue:000000000}";
                }
            }
        }
    }
        
    // Called when a player inventory item is dragged to the sell container
    public void HandlePlayerItemDraggedToSell(Item item, int quantity, int sourceIndex)
    {
        if (!isInSellMode) return;
        
        var sellContainer = shopSystem.GetSellContainer();
        if (sellContainer == null) return;
        
        // Find a place in the sell container for this item
        Vector2Int? position = sellContainer.FindSpaceForItem(item, true);
        if (position.HasValue)
        {
            int sellIndex = position.Value.y * sellContainer.GridWidth + position.Value.x;
            // Add to sell container
            sellContainer.AddItemToSlot(item, quantity, sellIndex);
            
            // Log for debugging
            Debug.Log($"HandlePlayerItemDraggedToSell: Added {quantity}x {item.itemName} to sell container at position {position.Value}");
            
            // Update the sell grid visualization
            UpdateSellGrid();
            
            // Update UI values
            UpdateSellInfoLabels();
        }
        else
        {
            Debug.LogWarning($"HandlePlayerItemDraggedToSell: Could not find space for {item.itemName} in sell container");
        }
    }

    public void UpdateCurrencyDisplay(int amount)
    {
        // Prevent redundant or too frequent updates
        if (isUpdatingCurrency)
        {
            Debug.Log($"[ShopUI] Skipping currency update to {amount} - already updating currency");
            return;
        }
        
        // If updates are too frequent, ensure we only use the latest value
        if (Time.time - lastCurrencyUpdateTime < CURRENCY_UPDATE_THROTTLE)
        {
            // Schedule the update for later with the latest amount
            StartCoroutine(DelayedCurrencyUpdate(amount));
            return;
        }
        
        isUpdatingCurrency = true;
        lastCurrencyUpdateTime = Time.time;
        
        try
        {
            // If the UI isn't initialized yet, store the amount and return
            if (root == null)
            {
                Debug.Log("[ShopUI] UI not initialized yet, queuing currency update for later");
                StartCoroutine(DelayedCurrencyUpdate(amount));
                return;
            }
            
            var moneyLabels = root.Query<Label>().Where(l => 
                l.text != null && 
                l.text.Contains("€") && 
                l != sellTotalValueLabel && 
                l != totalValueLabel &&
                !l.ClassListContains("shop-item-price")).ToList();
                
            // Log only once before updating all labels
            Debug.Log($"ShopUI: Updating {moneyLabels.Count} currency labels to {amount}");
            
            foreach (var label in moneyLabels)
            {
                // Extract the current displayed value, handling rich text properly
                int currentValue = ExtractCurrentValue(label.text);

                // Only update if there's an actual change
                if (currentValue != amount)
                {
                    // Always use the colored counter format
                    if (moneyCounterAnimation != null)
                    {
                        // Skip animation during startup or if we're just setting initial values
                        if (Time.timeSinceLevelLoad < 1f || currentValue == 0)
                        {
                            moneyCounterAnimation.SetColoredCounterValue(label, amount);
                        }
                        else
                        {
                            // Only animate if this is a real change during gameplay
                            bool isHeaderLabel = label.ClassListContains("header__money");
                            if (isHeaderLabel)
                            {
                                // Use the current displayed value as the start for animation
                                moneyCounterAnimation.AnimateMoneyCounter(label, currentValue, amount);
                                Debug.Log($"Animating currency from {currentValue} to {amount} on header label");
                            }
                            else
                            {
                                // For non-header labels, just set directly
                                moneyCounterAnimation.SetColoredCounterValue(label, amount);
                            }
                        }
                    }
                    else
                    {
                        // Fallback if no animation component
                        label.text = $"€ {amount:000000000}";
                    }
                }
            }
            
            // Also update price colors since currency has changed
            if (!isInSellMode)
            {
                UpdateItemPriceColors();
            }
        }
        finally
        {
            // Set a small delay before allowing next update to ensure animations complete
            StartCoroutine(ResetIsUpdatingCurrency());
        }
    }

    public void ShowSoldMessage(int amount)
    {
        // Update the Money Counter Animation if available
        if (moneyCounterAnimation != null)
        {
            moneyCounterAnimation.ShowAddedAmount(amount);
        }
        
        // Always update currency display from CurrencyManager
        if (CurrencyManager.Instance != null)
        {
            // Don't call UpdateCurrencyDisplay here as it will be handled by the animation
            // Just ensure we're in sync
            int currentCurrency = CurrencyManager.Instance.CurrentCurrency;
            Debug.Log($"[ShopUI] Sale completed, current currency is {currentCurrency}");
        }
        else
        {
            Debug.LogError("[ShopUI] CurrencyManager not found in ShowSoldMessage!");
        }
    }
    
    // New helper method to extract numeric value from rich text
    private int ExtractCurrentValue(string text)
    {
        if (string.IsNullOrEmpty(text))
            return 0;
            
        // Remove rich text tags
        string cleanText = System.Text.RegularExpressions.Regex.Replace(text, "<.*?>", string.Empty);
        
        // Remove all non-digit characters including € and € symbols
        string digitsOnly = new string(cleanText.Where(c => char.IsDigit(c)).ToArray());
        
        // Parse the resulting digits
        int result = 0;
        int.TryParse(digitsOnly, out result);
        
        return result;
    }
    
    // Update method to update currency display from CurrencyManager
    public void UpdateCurrencyFromManager()
    {
        // Prevent redundant updates
        if (isUpdatingCurrency || Time.time - lastCurrencyUpdateTime < CURRENCY_UPDATE_THROTTLE)
        {
            return;
        }
        
        // Always use CurrencyManager as the source of truth
        if (CurrencyManager.Instance != null)
        {
            int currentAmount = CurrencyManager.Instance.CurrentCurrency;
            
            // Check if any UI labels need updating before triggering an update
            bool needsUpdate = false;
            
            if (root != null)
            {
                var moneyLabels = root.Query<Label>().Where(l => 
                    l.text != null && 
                    l.text.Contains("€") && 
                    l != sellTotalValueLabel && 
                    l != totalValueLabel &&
                    !l.ClassListContains("shop-item-price")).ToList();
                    
                foreach (var label in moneyLabels)
                {
                    int displayedValue = ExtractCurrentValue(label.text);
                    if (displayedValue != currentAmount)
                    {
                        needsUpdate = true;
                        break;
                    }
                }
            }
            else
            {
                // If root isn't initialized, we'll need to update later
                needsUpdate = true;
            }
            
            if (needsUpdate)
            {
                Debug.Log($"[ShopUI] Updating currency display from CurrencyManager: {currentAmount}");
                UpdateCurrencyDisplay(currentAmount);
            }
        }
        else
        {
            Debug.LogError("[ShopUI] CurrencyManager not found! Creating one to ensure currency system works.");
            GameObject cmObj = new GameObject("CurrencyManager");
            CurrencyManager currencyManager = cmObj.AddComponent<CurrencyManager>();
            UpdateCurrencyDisplay(currencyManager.CurrentCurrency);
        }
    }
    
    // Add a small delay before allowing next currency update
    private IEnumerator ResetIsUpdatingCurrency()
    {
        yield return new WaitForSeconds(0.2f);
        isUpdatingCurrency = false;
    }
    
    private IEnumerator DelayedCurrencyUpdate(int amount)
    {
        // Wait until root is initialized
        float waitTime = 0f;
        while (root == null && waitTime < 5f)
        {
            waitTime += 0.1f;
            yield return new WaitForSeconds(0.1f);
        }
        
        if (root != null)
        {
            Debug.Log($"[ShopUI] Applying delayed currency update: {amount}");
            UpdateCurrencyDisplay(amount);
        }
        else
        {
            Debug.LogWarning("[ShopUI] Couldn't update currency display after 5 seconds - UI still not initialized");
        }
    }
    
    public void SetShopMode(bool sellMode)
    {
        isInSellMode = sellMode;
        
        // Clear selections when switching modes
        if (!sellMode)
        {
            ClearSelections();
            // Reset buy total value to gray 0 when switching to buy mode
            if (totalValueLabel != null)
            {
                totalValueLabel.text = "€ 0";
                totalValueLabel.style.color = new Color(0.6f, 0.6f, 0.6f);
            }
        }
        else
        {
            // Make sure the sell container is initialized
            var sellContainer = shopSystem.GetSellContainer();
            
            // Reset sell total value to gray 0 when switching to sell mode
            if (sellTotalValueLabel != null)
            {
                sellTotalValueLabel.text = "€ 0";
                sellTotalValueLabel.style.color = new Color(0.6f, 0.6f, 0.6f);
            }
            
            // Initialize the sell grid
            InitializeSellGrid();
        }
        
        // Update UI elements visibility
        if (buyModeContainer != null)
        {
            buyModeContainer.style.display = sellMode ? DisplayStyle.None : DisplayStyle.Flex;
            // Remove active-grid class when not in buy mode
            if (!sellMode)
            {
                var grids = buyModeContainer.Query<VisualElement>().Where(v => v.ClassListContains("active-grid")).ToList();
                foreach (var grid in grids)
                {
                    grid.RemoveFromClassList("active-grid");
                }
            }
        }
            
        if (sellModeContainer != null)
        {
            sellModeContainer.style.display = sellMode ? DisplayStyle.Flex : DisplayStyle.None;
            
            // Add a CSS class to the sell mode container for styling when active
            if (sellMode)
            {
                sellModeContainer.AddToClassList("sell-mode-active");
                // Ensure the sell grid gets focus
                sellModeContainer.Focus();
            }
            else
            {
                sellModeContainer.RemoveFromClassList("sell-mode-active");
            }
        }
        
        // Update the instruction text based on the mode
        if (instructionLabel != null)
        {
            instructionLabel.text = sellMode ? 
                "Drag items here to sell" : 
                "Click any item/s to select to buy";
                
            // Make the instructions more prominent in sell mode
            if (sellMode)
            {
                instructionLabel.AddToClassList("sell-instructions");
            }
            else
            {
                instructionLabel.RemoveFromClassList("sell-instructions");
            }
        }
            
        // Update button states
        if (buyModeButton != null)
        {
            buyModeButton.RemoveFromClassList("pixel-button--active");
            if (!sellMode) buyModeButton.AddToClassList("pixel-button--active");
        }
        
        if (sellModeButton != null)
        {
            sellModeButton.RemoveFromClassList("pixel-button--active");
            if (sellMode) sellModeButton.AddToClassList("pixel-button--active");
        }
        
        // After updating UI elements, make sure the currency display is consistent
        if (CurrencyManager.Instance != null)
        {
            int currentCurrency = CurrencyManager.Instance.CurrentCurrency;
            
            // Get all currency labels and make sure they're updated with current value
            if (root != null)
            {
                var currencyLabels = root.Query<Label>().Where(l => 
                    l.text != null && 
                    l.text.Contains("€") && 
                    l != sellTotalValueLabel && 
                    l != totalValueLabel &&
                    !l.ClassListContains("shop-item-price")).ToList();
                    
                // Only update if needed - use immediate update without animation
                foreach (var label in currencyLabels)
                {
                    int displayedValue = ExtractCurrentValue(label.text);
                    if (displayedValue != currentCurrency)
                    {
                        Debug.Log($"SetShopMode: Correcting currency label value from {displayedValue} to {currentCurrency}");
                        if (moneyCounterAnimation != null)
                        {
                            // Use SetColoredCounterValue directly to avoid animation
                            moneyCounterAnimation.SetColoredCounterValue(label, currentCurrency);
                        }
                        else
                        {
                            label.text = $"€ {currentCurrency:000000000}";
                        }
                    }
                }
            }
        }
        
        // Update UI
        UpdateUI();
    }
    
    // Start a coroutine to monitor the sell container for changes
    private void StartMonitoringSellContainer()
    {
        StartCoroutine(MonitorSellContainerChanges());
    }
    
    // Use a coroutine to check for changes in the sell container
    private IEnumerator MonitorSellContainerChanges()
    {
        int lastItemCount = 0;
        
        while (true)
        {
            // Only check when in sell mode
            if (isInSellMode)
            {
                var sellContainer = shopSystem.GetSellContainer();
                if (sellContainer != null)
                {
                    var items = sellContainer.GetItems();
                    int currentItemCount = items.Count;
                    
                    // If item count has changed, update the UI values
                    if (currentItemCount != lastItemCount)
                    {
                        lastItemCount = currentItemCount;
                        UpdateSellInfoLabels();
                        
                        // Remove currency update call - currency only updates on sale
                    }
                }
            }
            
            // Wait a short time before checking again
            yield return new WaitForSeconds(0.1f);
        }
    }

    

    // Update the sell grid visualization to support drag and drop
    private void UpdateSellGrid()
    {
        if (sellGrid == null) return;
        sellGrid.Clear();
        
        var sellContainer = shopSystem.GetSellContainer();
        if (sellContainer == null) return;
        
        // Debug.Log("UpdateSellGrid: Creating sell grid visualization");
        
        // Create containers for grid and items if they don't exist
        VisualElement gridContainer = new VisualElement();
        gridContainer.style.position = Position.Relative;
        
        sellGrid.Add(gridContainer);
        
        // Container for the items
        VisualElement itemsContainer = new VisualElement();
        itemsContainer.style.position = Position.Absolute;
        itemsContainer.style.left = 0;
        itemsContainer.style.top = 0;
        
        gridContainer.Add(itemsContainer);
        
        // Ensure we have a GridRenderer
        if (gridRenderer == null)
        {
            gridRenderer = GetComponent<GridRenderer>();
            if (gridRenderer == null)
            {
                gridRenderer = gameObject.AddComponent<GridRenderer>();
                gridRenderer.Initialize(uiDocument, equipmentManager, dragAndDropManager);
            }
        }
        
        // Use the same rendering approach as the stash grid
        gridRenderer.RenderStashGrid("SellGrid", sellContainer, sellContainer.GridWidth, sellContainer.GridHeight, 
            gridContainer, itemsContainer, "SellSlot");
        
        // Register for drag and drop for sell slots
        RegisterSellGridForDragAndDrop();
        
        // Debug.Log("UpdateSellGrid: Sell grid visualization created successfully");
    }
    
    // Register sell grid slots for drag and drop (similar to StashUI)
    private void RegisterSellGridForDragAndDrop()
    {
        if (dragAndDropManager == null || shopSystem == null || sellGrid == null) return;

        var sellContainer = shopSystem.GetSellContainer();
        if (sellContainer == null) return;

        // Register sell container slots for drag and drop
        for (int y = 0; y < sellContainer.GridHeight; y++)
        {
            for (int x = 0; x < sellContainer.GridWidth; x++)
            {
                int index = y * sellContainer.GridWidth + x;
                string slotType = $"SellSlot_{index}";
                
                // More efficient lookup with direct child selector
                var slot = sellGrid.Q<VisualElement>($"[name={slotType}]");
                if (slot != null)
                {
                    dragAndDropManager.RegisterSlotForDragging(slot, slotType);
                }
            }
        }
    }
    
    // Remove the custom RegisterSellGridCallbacks and replace with RegisterSellGridForDragAndDrop above
    private void RegisterSellGridCallbacks(VisualElement gridElement)
    {
        // This method is being replaced with RegisterSellGridForDragAndDrop
        // to make the behavior consistent with StashUI
    }

    // Make sure to properly initialize the sell grid when switching to sell mode
    private void InitializeSellGrid()
    {
        if (sellGrid == null) return;
        
        var sellContainer = shopSystem.GetSellContainer();
        if (sellContainer == null) return;
        
        // Ensure we have a GridRenderer
        if (gridRenderer == null)
        {
            gridRenderer = GetComponent<GridRenderer>();
            if (gridRenderer == null)
            {
                gridRenderer = gameObject.AddComponent<GridRenderer>();
                gridRenderer.Initialize(uiDocument, equipmentManager, dragAndDropManager);
            }
        }
        
        // Clear and prioritize the sell grid
        sellGrid.Clear();
        sellGrid.style.opacity = 1;
        sellGrid.AddToClassList("active-grid");
        
        // Set focus on the sell grid container to ensure keyboard events go to it
        if (sellModeContainer != null)
        {
            sellModeContainer.Focus();
        }
        
        // Update the sell grid UI
        UpdateSellGrid();
    }



    // New method to update price colors based on affordability
    private void UpdateItemPriceColors()
    {
        if (shopGrid == null || !shopGrid.ClassListContains("grid-container")) return;
        
        // Find all price labels in the shop grid
        var priceLabels = shopGrid.Query<Label>(className: "shop-item-price").ToList();
        
        foreach (var label in priceLabels)
        {
            if (label.parent != null && label.parent.userData != null)
            {
                string uniqueId = label.parent.userData.ToString();
                
                // The item is already stored in selectedItems dictionary by its uniqueId
                // Try to find the item reference directly in the element
                if (selectedItems.ContainsKey(uniqueId))
                {
                    // We already have the item reference in the selection
                    var item = selectedItems[uniqueId].Item;
                    
                    // Check if player can afford this item
                    int itemPrice = shopSystem.GetItemPrice(item);
                    bool canAfford = shopSystem.HasEnoughMoney(itemPrice);
                    
                    // Update price label color
                    if (canAfford)
                    {
                        label.RemoveFromClassList("price-cannot-afford");
                    }
                    else
                    {
                        label.AddToClassList("price-cannot-afford");
                    }
                }
                else
                {
                    // Get available items from the shop system
                    var availableItems = shopSystem.GetAvailableItems();
                    
                    // Extract the item name from the uniqueId (format is "itemName_GUID")
                    string itemName = uniqueId.Split('_')[0];
                    
                    // Find the item in available items
                    var item = availableItems.FirstOrDefault(i => i.itemName == itemName);
                    
                    if (item != null)
                    {
                        // Check if player can afford this item
                        int itemPrice = shopSystem.GetItemPrice(item);
                        bool canAfford = shopSystem.HasEnoughMoney(itemPrice);
                        
                        // Update price label color
                        if (canAfford)
                        {
                            label.RemoveFromClassList("price-cannot-afford");
                        }
                        else
                        {
                            label.AddToClassList("price-cannot-afford");
                        }
                    }
                }
            }
        }
    }

    // Add method to check if in sell mode (for InvShiftClickHandler)
    public bool IsInSellMode()
    {
        return isInSellMode;
    }
}