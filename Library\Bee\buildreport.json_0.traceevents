{ "pid": 11856, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 11856, "tid": 1, "ts": 1755527360854517, "dur": 30026, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11856, "tid": 1, "ts": 1755527360884548, "dur": 583704, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11856, "tid": 1, "ts": 1755527361468261, "dur": 10745, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 11856, "tid": 641, "ts": 1755527367970167, "dur": 1629, "ph": "X", "name": "", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360852360, "dur": 15441, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360867804, "dur": 7088723, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360869234, "dur": 1991, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360871230, "dur": 452, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360871683, "dur": 5809, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360877497, "dur": 159, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360877661, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360877705, "dur": 762, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360878470, "dur": 11558, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360890037, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360890041, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360890074, "dur": 634, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360890711, "dur": 35, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360890752, "dur": 214, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527360890968, "dur": 256792, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527361147765, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527361147769, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527361147826, "dur": 868, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527361148697, "dur": 6790896, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367939603, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367939607, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367939653, "dur": 1594, "ph": "X", "name": "ProcessMessages 15973", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367941249, "dur": 4697, "ph": "X", "name": "ReadAsync 15973", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367945951, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367946001, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367946356, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367946385, "dur": 243, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 11856, "tid": 12884901888, "ts": 1755527367946630, "dur": 9247, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 11856, "tid": 641, "ts": 1755527367971799, "dur": 34, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11856, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 11856, "tid": 8589934592, "ts": 1755527360848946, "dur": 630097, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 11856, "tid": 8589934592, "ts": 1755527361479046, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 11856, "tid": 8589934592, "ts": 1755527361479051, "dur": 1022, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 11856, "tid": 641, "ts": 1755527367971835, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 11856, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 11856, "tid": 4294967296, "ts": 1755527360781269, "dur": 7176335, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11856, "tid": 4294967296, "ts": 1755527360785832, "dur": 58281, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11856, "tid": 4294967296, "ts": 1755527367957660, "dur": 4968, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 11856, "tid": 4294967296, "ts": 1755527367960750, "dur": 31, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 11856, "tid": 4294967296, "ts": 1755527367962746, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 11856, "tid": 641, "ts": 1755527367971839, "dur": 107, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755527360866296, "dur":23482, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755527360889788, "dur":155, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755527360889975, "dur":411, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755527360890406, "dur":70, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755527360890477, "dur":7055923, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755527367946402, "dur":143, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755527367946569, "dur":73, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755527367946776, "dur":3417, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755527360891746, "dur":250595, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1755527360890667, "dur":256694, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1755527361149180, "dur":6791160, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1755527360890705, "dur":589796, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755527361491381, "dur":493, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1755527361480502, "dur":11377, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755527361491880, "dur":6454536, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755527360890706, "dur":601176, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755527361491883, "dur":6454516, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755527360890730, "dur":7055667, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755527360890755, "dur":7055641, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755527360890778, "dur":7055625, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755527360890806, "dur":7055598, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755527360890834, "dur":7055560, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755527360890863, "dur":7055548, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755527360890896, "dur":7055518, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755527360890944, "dur":7055464, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755527360890977, "dur":7055433, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755527367955191, "dur":423, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 11856, "tid": 641, "ts": 1755527367972529, "dur": 1597, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 11856, "tid": 641, "ts": 1755527367974254, "dur": 2006, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 11856, "tid": 641, "ts": 1755527367969214, "dur": 7931, "ph": "X", "name": "Write chrome-trace events", "args": {} },
