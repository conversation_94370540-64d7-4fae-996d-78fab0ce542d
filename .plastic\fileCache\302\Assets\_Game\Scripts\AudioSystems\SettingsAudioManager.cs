using UnityEngine;
using System;
using System.Reflection;
using UnityEngine.Audio;
using UnityEngine.SceneManagement;
using AudioSystem;

public class SettingsAudioManager : MonoBehaviour
{
    private static SettingsAudioManager _instance;
    public static SettingsAudioManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindAnyObjectByType<SettingsAudioManager>();
                if (_instance == null)
                {
                    var go = new GameObject("SettingsAudioManager");
                    _instance = go.AddComponent<SettingsAudioManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }

    public event Action<float> OnMasterVolumeChanged;
    public event Action<float> OnMusicVolumeChanged;
    public event Action<float> OnSFXVolumeChanged;
    public event Action<float> OnUIVolumeChanged;
    public event Action<bool> OnMuteChanged;

    [Header("Audio Configuration - Reference GlobalAudioManager")]
    [SerializeField] private GlobalAudioManager globalAudioManagerRef;
    
    // We'll get the mixer and groups from GlobalAudioManager instead of duplicating them
    private AudioMixer audioMixer;

    private float _masterVolume = 1f;
    private float _musicVolume = 1f;
    private float _sfxVolume = 1f;
    private float _uiVolume = 1f;
    private bool _isMuted = false;

    // Reference to GlobalAudioManager for coordination
    private GlobalAudioManager globalAudioManager;

    public float MasterVolume
    {
        get => _masterVolume;
        set
        {
            float clampedValue = Mathf.Clamp01(value);
            if (_masterVolume != clampedValue)
            {
                _masterVolume = clampedValue;
                SaveMasterVolumeSetting();
                ApplyMasterVolume();
                OnMasterVolumeChanged?.Invoke(_masterVolume);
                Debug.Log($"Master volume changed to: {_masterVolume}");
            }
        }
    }

    public float MusicVolume
    {
        get => _musicVolume;
        set
        {
            float clampedValue = Mathf.Clamp01(value);
            if (_musicVolume != clampedValue)
            {
                _musicVolume = clampedValue;
                SaveMusicVolumeSetting();
                ApplyMusicVolume();
                OnMusicVolumeChanged?.Invoke(_musicVolume);
                Debug.Log($"Music volume changed to: {_musicVolume}");
            }
        }
    }

    public float SFXVolume
    {
        get => _sfxVolume;
        set
        {
            float clampedValue = Mathf.Clamp01(value);
            if (_sfxVolume != clampedValue)
            {
                _sfxVolume = clampedValue;
                SaveSFXVolumeSetting();
                ApplySFXVolume();
                OnSFXVolumeChanged?.Invoke(_sfxVolume);
                Debug.Log($"SFX volume changed to: {_sfxVolume}");
            }
        }
    }

    public float UIVolume
    {
        get => _uiVolume;
        set
        {
            float clampedValue = Mathf.Clamp01(value);
            if (_uiVolume != clampedValue)
            {
                _uiVolume = clampedValue;
                SaveUIVolumeSetting();
                ApplyUIVolume();
                OnUIVolumeChanged?.Invoke(_uiVolume);
                Debug.Log($"UI volume changed to: {_uiVolume}");
            }
        }
    }

    public bool IsMuted
    {
        get => _isMuted;
        set
        {
            if (_isMuted != value)
            {
                _isMuted = value;
                SaveMuteSetting();
                ApplyAllVolumes();
                OnMuteChanged?.Invoke(_isMuted);
                Debug.Log($"Mute state changed to: {_isMuted}");
            }
        }
    }

    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Debug.LogWarning($"Multiple SettingsAudioManager instances found. Destroying duplicate on {gameObject.name}");
            Destroy(gameObject);
            return;
        }

        _instance = this;
        DontDestroyOnLoad(gameObject);

        Debug.Log("SettingsAudioManager initialized - Loading settings");
        LoadSettings();
    }

    private void Start()
    {
        TryEnsureAudioLinkAndApply();
        SceneManager.sceneLoaded += HandleSceneLoaded;
    }

    private void OnEnable()
    {
        // In case this component is re-enabled between scenes
        SceneManager.sceneLoaded += HandleSceneLoaded;
        TryEnsureAudioLinkAndApply();
    }

    private void OnDisable()
    {
        SceneManager.sceneLoaded -= HandleSceneLoaded;
    }

    private void HandleSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        TryEnsureAudioLinkAndApply();
    }

    private void TryEnsureAudioLinkAndApply()
    {
        // Prefer explicit reference if provided
        if (globalAudioManagerRef != null)
        {
            globalAudioManager = globalAudioManagerRef;
        }
        else if (globalAudioManager == null)
        {
            // Lazily acquire the singleton instance if available
            globalAudioManager = GlobalAudioManager.Instance;
        }

        if (globalAudioManager != null && audioMixer == null)
        {
            var field = typeof(GlobalAudioManager).GetField("audioMixer",
                BindingFlags.NonPublic | BindingFlags.Instance);
            if (field != null)
            {
                audioMixer = field.GetValue(globalAudioManager) as AudioMixer;
            }
        }

        ValidateAudioMixerSetup();
        ApplyAllVolumes();
    }

    private void ValidateAudioMixerSetup()
    {
        if (globalAudioManager == null)
        {
            Debug.LogError("SettingsAudioManager: No GlobalAudioManager reference found!");
            return;
        }
        
        if (audioMixer == null)
        {
            Debug.LogError("SettingsAudioManager: Could not get AudioMixer from GlobalAudioManager!");
        }
        else
        {
            Debug.Log("SettingsAudioManager: Successfully linked to GlobalAudioManager's AudioMixer");
        }
    }

    private void LoadSettings()
    {
        _masterVolume = Mathf.Clamp01(PlayerPrefs.GetFloat("SettingsMasterVolume", 1f));
        _musicVolume = Mathf.Clamp01(PlayerPrefs.GetFloat("SettingsMusicVolume", 1f));
        _sfxVolume = Mathf.Clamp01(PlayerPrefs.GetFloat("SettingsSFXVolume", 1f));
        _uiVolume = Mathf.Clamp01(PlayerPrefs.GetFloat("SettingsUIVolume", 1f));
        _isMuted = PlayerPrefs.GetInt("SettingsMuted", 0) == 1;

        Debug.Log("Loaded all audio settings from PlayerPrefs");
    }

    private void SaveMasterVolumeSetting()
    {
        PlayerPrefs.SetFloat("SettingsMasterVolume", _masterVolume);
        PlayerPrefs.Save();
    }

    private void SaveMusicVolumeSetting()
    {
        PlayerPrefs.SetFloat("SettingsMusicVolume", _musicVolume);
        PlayerPrefs.Save();
    }

    private void SaveSFXVolumeSetting()
    {
        PlayerPrefs.SetFloat("SettingsSFXVolume", _sfxVolume);
        PlayerPrefs.Save();
    }

    private void SaveUIVolumeSetting()
    {
        PlayerPrefs.SetFloat("SettingsUIVolume", _uiVolume);
        PlayerPrefs.Save();
    }

    private void SaveMuteSetting()
    {
        PlayerPrefs.SetInt("SettingsMuted", _isMuted ? 1 : 0);
        PlayerPrefs.Save();
    }

    private float ConvertToDecibels(float normalizedVolume)
    {
        if (normalizedVolume <= 0f)
            return -80f;
            
        return Mathf.Log10(normalizedVolume) * 20f;
    }

    private void ApplyVolumeToMixerGroup(AudioMixerGroupType groupType, float volume)
    {
        if (audioMixer == null || globalAudioManager == null) return;
        
        // Get the mixer group from GlobalAudioManager
        AudioMixerGroup targetGroup = GetMixerGroupFromGlobalManager(groupType);
        if (targetGroup == null) return;
        
        // Use the mixer group's name as the parameter name (standard Unity convention)
        string parameterName = $"{targetGroup.name}Volume";
        
        float decibelVolume = _isMuted ? -80f : ConvertToDecibels(volume);
        bool success = audioMixer.SetFloat(parameterName, decibelVolume);
        
        if (!success)
        {
            // Try alternative naming conventions
            string[] alternativeNames = {
                targetGroup.name,
                $"{groupType}Volume",
                groupType.ToString()
            };
            
            foreach (string altName in alternativeNames)
            {
                if (audioMixer.SetFloat(altName, decibelVolume))
                {
                    Debug.Log($"Applied {groupType} volume using parameter: {altName}");
                    return;
                }
            }
            
            Debug.LogWarning($"Could not find exposed parameter for {groupType}. " +
                           $"Tried: {parameterName}, {string.Join(", ", alternativeNames)}");
        }
        else
        {
            Debug.Log($"Applied {groupType} volume using parameter: {parameterName}");
        }
    }
    
    private AudioMixerGroup GetMixerGroupFromGlobalManager(AudioMixerGroupType groupType)
    {
        if (globalAudioManager == null) return null;
        
        // Use reflection to get the mixer groups from GlobalAudioManager
        var type = typeof(GlobalAudioManager);
        
        string fieldName = groupType switch
        {
            AudioMixerGroupType.Master => "masterGroup",
            AudioMixerGroupType.SFX => "sfxGroup",
            AudioMixerGroupType.UI => "uiGroup",
            AudioMixerGroupType.Music => "musicGroup",
            AudioMixerGroupType.Ambient => "ambientGroup",
            _ => "sfxGroup"
        };
        
        var field = type.GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return field?.GetValue(globalAudioManager) as AudioMixerGroup;
    }

    private void ApplyMasterVolume()
    {
        ApplyVolumeToMixerGroup(AudioMixerGroupType.Master, _masterVolume);
    }

    private void ApplyMusicVolume()
    {
        ApplyVolumeToMixerGroup(AudioMixerGroupType.Music, _musicVolume);
    }

    private void ApplySFXVolume()
    {
        ApplyVolumeToMixerGroup(AudioMixerGroupType.SFX, _sfxVolume);
    }

    private void ApplyUIVolume()
    {
        ApplyVolumeToMixerGroup(AudioMixerGroupType.UI, _uiVolume);
    }

    private void ApplyAllVolumes()
    {
        ApplyMasterVolume();
        ApplyMusicVolume();
        ApplySFXVolume();
        ApplyUIVolume();
        Debug.Log("Applied all audio settings");
    }

    public void ApplySettings()
    {
        ApplyAllVolumes();
    }

    public void ResetToDefaults()
    {
        _masterVolume = 1f;
        SaveMasterVolumeSetting();
        OnMasterVolumeChanged?.Invoke(_masterVolume);

        _musicVolume = 1f;
        SaveMusicVolumeSetting();
        OnMusicVolumeChanged?.Invoke(_musicVolume);

        _sfxVolume = 1f;
        SaveSFXVolumeSetting();
        OnSFXVolumeChanged?.Invoke(_sfxVolume);

        _uiVolume = 1f;
        SaveUIVolumeSetting();
        OnUIVolumeChanged?.Invoke(_uiVolume);

        _isMuted = false;
        SaveMuteSetting();
        OnMuteChanged?.Invoke(_isMuted);

        ApplyAllVolumes();

        Debug.Log("Reset all audio settings to defaults");
    }

    // Method to sync with GlobalAudioManager if needed
    public void SyncWithGlobalAudioManager()
    {
        if (globalAudioManager != null)
        {
            // Both managers now use the same AudioMixer and groups
            Debug.Log("SettingsAudioManager synced with GlobalAudioManager");
            ApplyAllVolumes(); // Re-apply settings with the synced configuration
        }
    }
    
    // Utility method to get all available AudioMixerGroupTypes that GlobalAudioManager supports
    public AudioMixerGroupType[] GetSupportedMixerGroups()
    {
        return new AudioMixerGroupType[]
        {
            AudioMixerGroupType.Master,
            AudioMixerGroupType.SFX,
            AudioMixerGroupType.UI,
            AudioMixerGroupType.Music,
            AudioMixerGroupType.Ambient
        };
    }
}