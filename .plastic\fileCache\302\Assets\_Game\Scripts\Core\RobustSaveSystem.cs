using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;

/// <summary>
/// Bulletproof save system with multiple redundancy layers and automatic recovery
/// </summary>
public class RobustSaveSystem : MonoBehaviour
{
    [System.Serializable]
    public class SaveMetadata
    {
        public string saveVersion = "2.0";
        public string gameVersion;
        public long timestamp;
        public string checksum;
        public bool isAutoSave;
        public bool isCheckpoint;
        public int saveNumber;
        public float playTime;
        public string sceneName;
        public Vector3 playerPosition;
        
        public SaveMetadata()
        {
            gameVersion = Application.version;
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        }
    }
    
    [System.Serializable]
    public class SaveState
    {
        public SaveMetadata metadata;
        public PersistenceManager.ProgressionData progressionData;
        public string jsonData; // For debugging
        
        public bool IsValid()
        {
            if (metadata == null || progressionData == null) return false;
            if (progressionData.playerPosition == Vector3.zero) return false;
            if (string.IsNullOrEmpty(metadata.checksum)) return false;
            
            // Verify checksum
            string currentChecksum = GenerateChecksum(progressionData);
            return currentChecksum == metadata.checksum;
        }
        
        public static string GenerateChecksum(PersistenceManager.ProgressionData data)
        {
            string json = JsonUtility.ToJson(data);
            return ComputeHash(json);
        }
        
        private static string ComputeHash(string input)
        {
            using (var sha = System.Security.Cryptography.SHA256.Create())
            {
                byte[] bytes = System.Text.Encoding.UTF8.GetBytes(input);
                byte[] hash = sha.ComputeHash(bytes);
                return Convert.ToBase64String(hash);
            }
        }
    }
    
    // Multiple save slots for redundancy
    private const int MAX_ROTATING_SAVES = 5;  // Keep 5 rotating saves
    private const int MAX_CHECKPOINTS = 3;     // Keep 3 checkpoint saves
    private const int MAX_MEMORY_CACHE = 10;   // Keep 10 saves in memory
    
    // Save intervals
    private const float AUTO_SAVE_INTERVAL = 60f;      // Auto-save every minute
    private const float CHECKPOINT_INTERVAL = 300f;    // Checkpoint every 5 minutes
    private const float QUICK_SAVE_INTERVAL = 0.25f;   // Position-only saves
    
    // File paths
    private string SaveDirectory => Path.Combine(Application.persistentDataPath, "saves");
    private string CurrentSaveFile => Path.Combine(SaveDirectory, "current.sav");
    private string GetRotatingSaveFile(int index) => Path.Combine(SaveDirectory, $"auto_{index}.sav");
    private string GetCheckpointFile(int index) => Path.Combine(SaveDirectory, $"checkpoint_{index}.sav");
    private string RecoveryFile => Path.Combine(SaveDirectory, "recovery.sav");
    private string CrashBackupFile => Path.Combine(SaveDirectory, "crash_backup.sav");
    
    // In-memory cache of recent valid saves
    private Queue<SaveState> memorySaveCache = new Queue<SaveState>();
    private SaveState lastKnownGoodSave;
    private SaveState currentActiveSave;
    public bool HasValidSave => lastKnownGoodSave != null || (currentActiveSave != null && currentActiveSave.IsValid());

    // Optional lightweight position history to assist repairs
    private PositionHistory positionHistory = new PositionHistory();

    private class PositionHistory
    {
        private Queue<(Vector3 position, float time)> history = new Queue<(Vector3, float)>(100);

        public void Add(Vector3 pos)
        {
            if (pos == Vector3.zero) return;
            history.Enqueue((pos, Time.time));
            while (history.Count > 100) history.Dequeue();
        }

        public Vector3? GetLastValidPosition(float maxAge = 60f)
        {
            float now = Time.time;
            var arr = history.ToArray();
            for (int i = arr.Length - 1; i >= 0; i--)
            {
                var entry = arr[i];
                if (now - entry.time < maxAge && entry.position != Vector3.zero)
                    return entry.position;
            }
            return null;
        }
    }
    
    // Tracking
    private int currentRotatingIndex = 0;
    private int currentCheckpointIndex = 0;
    private float timeSinceLastAutoSave = 0f;
    private float timeSinceLastCheckpoint = 0f;
    private float playTimeAtStart = 0f;
    private bool hasDetectedCrash = false;
    
    // References (from your existing system)
    private PersistenceManager persistenceManager;
    
    private void Awake()
    {
        // Create save directory if it doesn't exist
        if (!Directory.Exists(SaveDirectory))
        {
            Directory.CreateDirectory(SaveDirectory);
        }
        
        persistenceManager = GetComponent<PersistenceManager>();
        
        // Detect if we crashed last time
        DetectAndHandleCrash();
        
        // Load the most recent valid save
        LoadBestAvailableSave();
        
        // Start tracking play time
        playTimeAtStart = Time.time;
    }
    
    private void Start()
    {
        // Start auto-save coroutine
        StartCoroutine(AutoSaveCoroutine());
        StartCoroutine(CheckpointCoroutine());
    }
    
    /// <summary>
    /// Main save method - validates and saves with multiple redundancy layers
    /// </summary>
    public bool SaveGame(bool isAutoSave = false, bool isCheckpoint = false, bool emergency = false)
    {
        try
        {
            // 1. Create new save state
            SaveState newSave = CreateSaveState(isAutoSave, isCheckpoint);
            
            // 2. Validate the new save
            if (!emergency && !ValidateSaveState(newSave))
            {
                Debug.LogError("[SaveSystem] New save state validation failed!");
                
                // Try to fix common issues
                if (TryRepairSaveState(newSave))
                {
                    Debug.Log("[SaveSystem] Successfully repaired save state");
                }
                else
                {
                    Debug.LogError("[SaveSystem] Could not repair save state - aborting save");
                    return false;
                }
            }
            
            // 3. Add to memory cache
            AddToMemoryCache(newSave);
            
            // 4. Save to appropriate files
            bool success = false;
            
            if (emergency)
            {
                success = SaveToFile(CrashBackupFile, newSave);
                Debug.Log("[SaveSystem] Emergency save completed");
            }
            else if (isCheckpoint)
            {
                success = SaveCheckpoint(newSave);
            }
            else if (isAutoSave)
            {
                success = SaveRotating(newSave);
            }
            else
            {
                success = SaveCurrent(newSave);
            }
            
            // 5. Update tracking
            if (success)
            {
                currentActiveSave = newSave;
                if (newSave.IsValid())
                {
                    lastKnownGoodSave = newSave;
                }
            }
            
            return success;
        }
        catch (Exception e)
        {
            Debug.LogError($"[SaveSystem] Critical save error: {e.Message}\n{e.StackTrace}");
            
            // Emergency fallback - try to at least save position
            EmergencyPositionSave();
            return false;
        }
    }
    
    /// <summary>
    /// Load the best available save from all sources
    /// </summary>
    public bool LoadBestAvailableSave()
    {
        Debug.Log("[SaveSystem] Loading best available save...");
        
        // Priority order:
        // 1. Current save (if valid)
        // 2. Most recent memory cache
        // 3. Most recent rotating save
        // 4. Most recent checkpoint
        // 5. Recovery file
        // 6. Crash backup
        // 7. Any older save files
        
        SaveState loadedSave = null;
        
        // Try current save
        if (File.Exists(CurrentSaveFile))
        {
            loadedSave = LoadFromFile(CurrentSaveFile);
            if (loadedSave != null && loadedSave.IsValid())
            {
                Debug.Log("[SaveSystem] Loaded from current save file");
                ApplySaveState(loadedSave);
                return true;
            }
        }
        
        // Try memory cache
        if (memorySaveCache.Count > 0)
        {
            var cacheArray = memorySaveCache.ToArray();
            for (int i = cacheArray.Length - 1; i >= 0; i--)
            {
                if (cacheArray[i] != null && cacheArray[i].IsValid())
                {
                    Debug.Log($"[SaveSystem] Loaded from memory cache (entry {i})");
                    ApplySaveState(cacheArray[i]);
                    return true;
                }
            }
        }
        
        // Try rotating saves (newest first)
        var rotating = Enumerable.Range(0, MAX_ROTATING_SAVES)
            .Select(i => GetRotatingSaveFile(i))
            .Where(File.Exists)
            .Select(p => new FileInfo(p))
            .OrderByDescending(fi => fi.LastWriteTime)
            .ToList();
        for (int i = 0; i < rotating.Count; i++)
        {
            string path = rotating[i].FullName;
            loadedSave = LoadFromFile(path);
            if (loadedSave != null && loadedSave.IsValid())
            {
                Debug.Log($"[SaveSystem] Loaded from rotating save {Path.GetFileName(path)}");
                ApplySaveState(loadedSave);
                return true;
            }
        }
        
        // Try checkpoints
        for (int i = 0; i < MAX_CHECKPOINTS; i++)
        {
            string path = GetCheckpointFile(i);
            if (File.Exists(path))
            {
                loadedSave = LoadFromFile(path);
                if (loadedSave != null && loadedSave.IsValid())
                {
                    Debug.Log($"[SaveSystem] Loaded from checkpoint {i}");
                    ApplySaveState(loadedSave);
                    return true;
                }
            }
        }
        
        // Try recovery file
        if (File.Exists(RecoveryFile))
        {
            loadedSave = LoadFromFile(RecoveryFile);
            if (loadedSave != null && loadedSave.IsValid())
            {
                Debug.Log("[SaveSystem] Loaded from recovery file");
                ApplySaveState(loadedSave);
                return true;
            }
        }
        
        // Try crash backup (require non-zero position)
        if (File.Exists(CrashBackupFile))
        {
            loadedSave = LoadFromFile(CrashBackupFile);
            if (loadedSave != null && loadedSave.progressionData != null && loadedSave.progressionData.playerPosition != Vector3.zero)
            {
                Debug.LogWarning("[SaveSystem] Loaded from crash backup (validated)");
                ApplySaveState(loadedSave);
                return true;
            }
        }
        
        // Last resort - scan all .sav files and load the newest valid one
        var allSaves = Directory.GetFiles(SaveDirectory, "*.sav")
            .OrderByDescending(File.GetLastWriteTime)
            .ToList();
        
        foreach (string savePath in allSaves)
        {
            loadedSave = LoadFromFile(savePath);
            if (loadedSave != null)
            {
                Debug.LogWarning($"[SaveSystem] Loaded from fallback save: {Path.GetFileName(savePath)}");
                ApplySaveState(loadedSave);
                return true;
            }
        }
        
        Debug.LogError("[SaveSystem] No valid save found - starting fresh");
        return false;
    }
    
    /// <summary>
    /// Create a save state from current game state
    /// </summary>
    private SaveState CreateSaveState(bool isAutoSave, bool isCheckpoint)
    {
        var save = new SaveState();
        save.metadata = new SaveMetadata();
        save.metadata.isAutoSave = isAutoSave;
        save.metadata.isCheckpoint = isCheckpoint;
        save.metadata.playTime = Time.time - playTimeAtStart;
        save.metadata.sceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        
        // Get progression data from your existing system
        var pmData = persistenceManager?.GetProgressionData();
        if (pmData != null)
        {
            save.progressionData = pmData;
            save.metadata.playerPosition = save.progressionData.playerPosition;
            if (save.metadata.playerPosition != Vector3.zero)
            {
                positionHistory.Add(save.metadata.playerPosition);
            }
        }
        else
        {
            save.progressionData = new PersistenceManager.ProgressionData();
        }
        
        // Generate checksum
        save.metadata.checksum = SaveState.GenerateChecksum(save.progressionData);
        
        // Store JSON for debugging
        save.jsonData = JsonUtility.ToJson(save.progressionData);
        
        return save;
    }
    
    /// <summary>
    /// Validate a save state before committing
    /// </summary>
    private bool ValidateSaveState(SaveState save)
    {
        if (save == null || save.progressionData == null)
            return false;
        
        // Check for critical data
        if (save.progressionData.playerPosition == Vector3.zero)
        {
            Debug.LogWarning("[SaveSystem] Validation failed: Player position is zero");
            return false;
        }
        
        // Check for NaN or infinity
        if (!IsFinite(save.progressionData.playerPosition))
        {
            Debug.LogWarning("[SaveSystem] Validation failed: Player position is NaN or infinite");
            return false;
        }
        
        // Check for suspicious jumps (only if we have a previous save)
        if (lastKnownGoodSave != null)
        {
            float distance = Vector3.Distance(
                save.progressionData.playerPosition,
                lastKnownGoodSave.progressionData.playerPosition
            );
            
            float timeDelta = save.metadata.timestamp - lastKnownGoodSave.metadata.timestamp;
            
            // If player moved more than 500 units in less than 1 second, it's suspicious
            if (distance > 500f && timeDelta < 1f)
            {
                Debug.LogWarning($"[SaveSystem] Validation warning: Large position jump detected ({distance}m in {timeDelta}s)");
                // Don't fail validation, just warn
            }
        }
        
        return true;
    }
    
    /// <summary>
    /// Try to repair a save state that failed validation
    /// </summary>
    private bool TryRepairSaveState(SaveState save)
    {
        if (save == null || save.progressionData == null)
            return false;
        
        bool repaired = false;
        
        // Fix zero position
        if (save.progressionData.playerPosition == Vector3.zero)
        {
            // Try to use last known good position
            if (lastKnownGoodSave != null)
            {
                save.progressionData.playerPosition = lastKnownGoodSave.progressionData.playerPosition;
                Debug.Log("[SaveSystem] Repaired zero position using last known good save");
                repaired = true;
            }
            // Try memory cache
            else if (memorySaveCache.Count > 0)
            {
                var lastCache = memorySaveCache.Last();
                if (lastCache != null && lastCache.progressionData.playerPosition != Vector3.zero)
                {
                    save.progressionData.playerPosition = lastCache.progressionData.playerPosition;
                    Debug.Log("[SaveSystem] Repaired zero position using memory cache");
                    repaired = true;
                }
            }
            // Try PlayerPrefs
            else if (PlayerPrefs.HasKey("player_pos_x"))
            {
                save.progressionData.playerPosition = new Vector3(
                    PlayerPrefs.GetFloat("player_pos_x"),
                    PlayerPrefs.GetFloat("player_pos_y"),
                    PlayerPrefs.GetFloat("player_pos_z")
                );
                Debug.Log("[SaveSystem] Repaired zero position using PlayerPrefs");
                repaired = true;
            }
            // Try recent position history
            else
            {
                var hist = positionHistory.GetLastValidPosition(120f);
                if (hist.HasValue)
                {
                    save.progressionData.playerPosition = hist.Value;
                    Debug.Log("[SaveSystem] Repaired zero position using PositionHistory");
                    repaired = true;
                }
            }
        }
        
        // Fix NaN/Infinity
        if (!IsFinite(save.progressionData.playerPosition))
        {
            if (lastKnownGoodSave != null)
            {
                save.progressionData.playerPosition = lastKnownGoodSave.progressionData.playerPosition;
                Debug.Log("[SaveSystem] Repaired NaN/Infinite position");
                repaired = true;
            }
        }
        
        // Regenerate checksum after repairs
        if (repaired)
        {
            save.metadata.checksum = SaveState.GenerateChecksum(save.progressionData);
        }
        
        return repaired;
    }
    
    /// <summary>
    /// Add save to memory cache
    /// </summary>
    private void AddToMemoryCache(SaveState save)
    {
        if (save == null) return;
        
        memorySaveCache.Enqueue(save);
        
        // Limit cache size
        while (memorySaveCache.Count > MAX_MEMORY_CACHE)
        {
            memorySaveCache.Dequeue();
        }
    }
    
    /// <summary>
    /// Save to current save file
    /// </summary>
    private bool SaveCurrent(SaveState save)
    {
        // Back up existing current save first
        if (File.Exists(CurrentSaveFile))
        {
            string backup = CurrentSaveFile + ".bak";
            File.Copy(CurrentSaveFile, backup, true);
        }
        
        return SaveToFile(CurrentSaveFile, save);
    }
    
    /// <summary>
    /// Save to rotating auto-save slot
    /// </summary>
    private bool SaveRotating(SaveState save)
    {
        string path = GetRotatingSaveFile(currentRotatingIndex);
        bool success = SaveToFile(path, save);
        
        if (success)
        {
            currentRotatingIndex = (currentRotatingIndex + 1) % MAX_ROTATING_SAVES;
            // Refresh recovery file on successful auto-save
            SaveToFile(RecoveryFile, save);
        }
        
        return success;
    }
    
    /// <summary>
    /// Save checkpoint
    /// </summary>
    private bool SaveCheckpoint(SaveState save)
    {
        string path = GetCheckpointFile(currentCheckpointIndex);
        bool success = SaveToFile(path, save);
        
        if (success)
        {
            currentCheckpointIndex = (currentCheckpointIndex + 1) % MAX_CHECKPOINTS;
            
            // Also save as recovery file
            SaveToFile(RecoveryFile, save);
        }
        
        return success;
    }
    
    /// <summary>
    /// Save state to file with encryption
    /// </summary>
    private bool SaveToFile(string path, SaveState save)
    {
        try
        {
            string json = JsonUtility.ToJson(save);
            string encrypted = EncryptString(json);
            
            // Write to temp file first
            string tempPath = path + ".tmp";
            File.WriteAllText(tempPath, encrypted);
            
            // Verify temp file
            if (!File.Exists(tempPath) || new FileInfo(tempPath).Length == 0)
            {
                throw new Exception("Temp file write failed");
            }
            
            // Move temp to final
            if (File.Exists(path))
            {
                File.Delete(path);
            }
            File.Move(tempPath, path);
            
            // Verify by reading back critical fields
            if (!VerifySaveFile(path, save))
            {
                Debug.LogError($"[SaveSystem] Post-write verification failed for {Path.GetFileName(path)}");
                return false;
            }

            Debug.Log($"[SaveSystem] Saved to {Path.GetFileName(path)}");
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"[SaveSystem] Failed to save to {path}: {e.Message}");
            return false;
        }
    }

    private bool VerifySaveFile(string path, SaveState originalSave)
    {
        SaveState readBack = LoadFromFile(path);
        if (readBack == null) return false;
        return readBack.progressionData.playerPosition == originalSave.progressionData.playerPosition &&
               readBack.metadata.checksum == originalSave.metadata.checksum;
    }
    
    /// <summary>
    /// Load state from file
    /// </summary>
    private SaveState LoadFromFile(string path)
    {
        try
        {
            if (!File.Exists(path))
                return null;
            
            string encrypted = File.ReadAllText(path);
            string json = DecryptString(encrypted);
            
            if (string.IsNullOrEmpty(json))
                return null;
            
            SaveState save = JsonUtility.FromJson<SaveState>(json);
            
            // Verify checksum
            if (save != null && save.progressionData != null)
            {
                string expectedChecksum = SaveState.GenerateChecksum(save.progressionData);
                if (save.metadata.checksum != expectedChecksum)
                {
                    Debug.LogWarning($"[SaveSystem] Checksum mismatch in {Path.GetFileName(path)}");
                    // Don't reject it entirely, just warn
                }
            }
            
            return save;
        }
        catch (Exception e)
        {
            Debug.LogError($"[SaveSystem] Failed to load from {path}: {e.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// Apply a loaded save state to the game
    /// </summary>
    private void ApplySaveState(SaveState save)
    {
        if (save == null || save.progressionData == null)
            return;
        
        // Apply to your existing persistence manager
        if (persistenceManager != null)
        {
            persistenceManager.ReplaceProgressionData(save.progressionData);
            
            // Trigger restoration
            persistenceManager.SendMessage("RestorePlayerPosition", SendMessageOptions.DontRequireReceiver);
            persistenceManager.SendMessage("RestoreInventoryAndEquipment", SendMessageOptions.DontRequireReceiver);
            persistenceManager.SendMessage("RestoreWorldItems", SendMessageOptions.DontRequireReceiver);

            // Shadow-write to PlayerPrefs as a safety net for next boot
            var pd = save.progressionData;
            if (pd.playerPosition != Vector3.zero)
            {
                PlayerPrefs.SetFloat("player_pos_x", pd.playerPosition.x);
                PlayerPrefs.SetFloat("player_pos_y", pd.playerPosition.y);
                PlayerPrefs.SetFloat("player_pos_z", pd.playerPosition.z);
                PlayerPrefs.SetString("player_scene", pd.playerSceneName);
                PlayerPrefs.SetFloat("player_pos_timestamp", Time.time);
                PlayerPrefs.Save();
            }
        }
        
        // Update tracking
        currentActiveSave = save;
        if (save.IsValid())
        {
            lastKnownGoodSave = save;
        }
    }
    
    /// <summary>
    /// Detect if the game crashed last time and offer recovery
    /// </summary>
    private void DetectAndHandleCrash()
    {
        // Check if crash backup exists and is newer than current save
        if (File.Exists(CrashBackupFile))
        {
            var crashInfo = new FileInfo(CrashBackupFile);
            bool isNewer = true;
            
            if (File.Exists(CurrentSaveFile))
            {
                var currentInfo = new FileInfo(CurrentSaveFile);
                isNewer = crashInfo.LastWriteTime > currentInfo.LastWriteTime;
            }
            
            if (isNewer)
            {
                hasDetectedCrash = true;
                Debug.LogWarning("[SaveSystem] Detected previous crash - crash backup available");
                
                // In a real game, you'd show a UI prompt here
                // For now, auto-recover
                SaveState crashSave = LoadFromFile(CrashBackupFile);
                if (crashSave != null && crashSave.progressionData != null && crashSave.progressionData.playerPosition != Vector3.zero)
                {
                    Debug.Log("[SaveSystem] Recovering from crash backup...");
                    ApplySaveState(crashSave);
                }
                else
                {
                    Debug.LogWarning("[SaveSystem] Crash backup invalid or empty; skipping auto-recovery");
                }
            }
        }
    }
    
    /// <summary>
    /// Emergency save that bypasses all validation
    /// </summary>
    private void EmergencyPositionSave()
    {
        try
        {
            var emergencyData = new
            {
                position = persistenceManager?.GetProgressionData()?.playerPosition ?? Vector3.zero,
                scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };
            
            string json = JsonUtility.ToJson(emergencyData);
            File.WriteAllText(Path.Combine(SaveDirectory, "emergency_position.json"), json);
            
            // Also to PlayerPrefs
            if (emergencyData.position != Vector3.zero)
            {
                PlayerPrefs.SetFloat("emergency_pos_x", emergencyData.position.x);
                PlayerPrefs.SetFloat("emergency_pos_y", emergencyData.position.y);
                PlayerPrefs.SetFloat("emergency_pos_z", emergencyData.position.z);
                PlayerPrefs.Save();
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[SaveSystem] Emergency save failed: {e.Message}");
        }
    }
    
    /// <summary>
    /// Auto-save coroutine
    /// </summary>
    private IEnumerator AutoSaveCoroutine()
    {
        while (true)
        {
            yield return new WaitForSeconds(AUTO_SAVE_INTERVAL);
            
            if (!PersistenceManager.IsRestoring)  // Don't save during restoration
            {
                SaveGame(isAutoSave: true);
            }
        }
    }
    
    /// <summary>
    /// Checkpoint coroutine
    /// </summary>
    private IEnumerator CheckpointCoroutine()
    {
        while (true)
        {
            yield return new WaitForSeconds(CHECKPOINT_INTERVAL);
            
            if (!PersistenceManager.IsRestoring)
            {
                SaveGame(isCheckpoint: true);
                Debug.Log("[SaveSystem] Checkpoint saved");
            }
        }
    }
    
    private void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            SaveGame(emergency: true);
        }
    }
    
    private void OnApplicationFocus(bool hasFocus)
    {
        if (!hasFocus)
        {
            SaveGame(emergency: true);
        }
    }
    
    private void OnApplicationQuit()
    {
        SaveGame(emergency: true);
        
        // Clean up old crash backup since we're quitting normally
        if (File.Exists(CrashBackupFile))
        {
            File.Delete(CrashBackupFile);
        }
    }
    
    private void OnDestroy()
    {
        SaveGame(emergency: true);
    }
    
    // Helpers
    private bool IsFinite(Vector3 v)
    {
        return !float.IsNaN(v.x) && !float.IsNaN(v.y) && !float.IsNaN(v.z) &&
               !float.IsInfinity(v.x) && !float.IsInfinity(v.y) && !float.IsInfinity(v.z);
    }
    
    private string EncryptString(string text)
    {
        // Reuse PersistenceManager's encryption to maintain compatibility
        if (persistenceManager != null)
        {
            return persistenceManager.EncryptForSave(text);
        }
        return text;
    }
    
    private string DecryptString(string text)
    {
        // Reuse PersistenceManager's decryption to maintain compatibility
        if (persistenceManager != null)
        {
            return persistenceManager.DecryptFromSave(text);
        }
        return text;
    }
    
    /// <summary>
    /// Get save system status for UI
    /// </summary>
    public string GetSaveSystemStatus()
    {
        var status = new System.Text.StringBuilder();
        status.AppendLine($"Save System Status:");
        status.AppendLine($"- Memory Cache: {memorySaveCache.Count} saves");
        status.AppendLine($"- Last Good Save: {(lastKnownGoodSave != null ? "Available" : "None")}");
        status.AppendLine($"- Current Save: {(currentActiveSave != null ? "Loaded" : "None")}");
        status.AppendLine($"- Crash Detected: {hasDetectedCrash}");
        
        // Check file existence
        status.AppendLine($"\nSave Files:");
        status.AppendLine($"- Current: {File.Exists(CurrentSaveFile)}");
        status.AppendLine($"- Recovery: {File.Exists(RecoveryFile)}");
        status.AppendLine($"- Crash Backup: {File.Exists(CrashBackupFile)}");
        
        int rotatingCount = 0;
        for (int i = 0; i < MAX_ROTATING_SAVES; i++)
        {
            if (File.Exists(GetRotatingSaveFile(i)))
                rotatingCount++;
        }
        status.AppendLine($"- Rotating Saves: {rotatingCount}/{MAX_ROTATING_SAVES}");
        
        int checkpointCount = 0;
        for (int i = 0; i < MAX_CHECKPOINTS; i++)
        {
            if (File.Exists(GetCheckpointFile(i)))
                checkpointCount++;
        }
        status.AppendLine($"- Checkpoints: {checkpointCount}/{MAX_CHECKPOINTS}");
        
        return status.ToString();
    }
}