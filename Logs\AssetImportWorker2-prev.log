[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias" at "2025-08-17T18:40:26.6388662Z"
Built from '6000.2/respin/6000.2.0f1-517f89d850d1' branch; Version is '6000.2.0f1 (eed1c594c913) revision 15651269'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-17T18:40:26Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.2.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker2.log
-srvPort
50817
-licensingIpc
LicenseClient-ilias
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [43120]  Target information:

Player connection [43120]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1962543842 [EditorId] 1962543842 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [43120]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1962543842 [EditorId] 1962543842 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [43120]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1962543842 [EditorId] 1962543842 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [43120]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1962543842 [EditorId] 1962543842 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [43120]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1962543842 [EditorId] 1962543842 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [43120] Host joined multi-casting on [***********:54997]...
Player connection [43120] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 33088, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.1'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-ilias"
[Licensing::IpcConnector] LicenseClient-ilias channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0" at "2025-08-17T18:40:26.8384524Z"
Library Redirect Path: Library/
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 34452, path: "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              cafb20a40a64401ea9b5faa7089eef4e
  Correlation Id:          07b42c3946518dbd4b3a31c74382467b
  External correlation Id: 4282006374037872002
  Machine Id:              /GaE4A2aXaDfTh/RbExLPR8H29M=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-ilias-6000.2.0" (connect: 0.00s, validation: 0.01s, handshake: 0.05s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0-notifications" at "2025-08-17T18:40:26.8957746Z"
[Licensing::Module] Licensing Background thread has ended after 0.26s
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1588.68 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.8088
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56700
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004457 seconds.
- Loaded All Assemblies, in  1.309 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.478 seconds
Domain Reload Profiling: 1777ms
	BeginReloadAssembly (266ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (219ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (733ms)
		LoadAssemblies (249ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (729ms)
			TypeCache.Refresh (727ms)
				TypeCache.ScanAssembly (709ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (479ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (405ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (76ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (156ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.447 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 17.69 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.387 seconds
Domain Reload Profiling: 2812ms
	BeginReloadAssembly (292ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (1020ms)
		LoadAssemblies (798ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (403ms)
			TypeCache.Refresh (329ms)
				TypeCache.ScanAssembly (255ms)
			BuildScriptInfoCaches (58ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1387ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1230ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (263ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (421ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 34.09 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (7.7 MB). Loaded Objects now: 8980.
Memory consumption went from 225.7 MB to 218.0 MB.
Total: 19.196800 ms (FindLiveObjects: 1.175600 ms CreateObjectMapping: 1.778400 ms MarkObjects: 10.210400 ms  DeleteObjects: 6.030900 ms)

========================================================================
Received Import Request.
  Time since last request: 2416.147091 seconds.
  path: Assets/Settings/Build Profiles/New Windows Profile.asset
  artifactKey: Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Build Profiles/New Windows Profile.asset using Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7598324d189924582b728cebfb49643f') in 0.1120609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1.611646 seconds.
  path: Assets/Settings/Build Profiles/New Windows Profile.asset
  artifactKey: Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Build Profiles/New Windows Profile.asset using Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fac280e711f5daae11e5da04549aca8f') in 0.0074503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 103.705866 seconds.
  path: ProjectSettings/EditorBuildSettings.asset
  artifactKey: Guid(0000000000000000b000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing ProjectSettings/EditorBuildSettings.asset using Guid(0000000000000000b000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c21e11646ea6cdaff9972971c6202555') in 0.0015198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.567080 seconds.
  path: Assets/Settings/Build Profiles/New Windows Profile.asset
  artifactKey: Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Build Profiles/New Windows Profile.asset using Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aafa2d3616f7068dab6aba8fb5e05c05') in 0.0069681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 259.142267 seconds.
  path: Assets/Settings/Build Profiles/New Windows Profile.asset
  artifactKey: Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Build Profiles/New Windows Profile.asset using Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db3203389e02c3aba95b9e3b96ebffa4') in 0.0042051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.957651 seconds.
  path: Assets/_Game/Scenes/Main.unity
  artifactKey: Guid(80207517658dd27438d88dd267d39966) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main.unity using Guid(80207517658dd27438d88dd267d39966) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95d36b844af58e2c9c3c5ca6c01bba9e') in 0.0007896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.78 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7932 unused Assets / (5.7 MB). Loaded Objects now: 8999.
Memory consumption went from 227.8 MB to 222.1 MB.
Total: 14.553400 ms (FindLiveObjects: 1.174300 ms CreateObjectMapping: 0.725500 ms MarkObjects: 9.055700 ms  DeleteObjects: 3.596300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1480.836356 seconds.
  path: Assets/_Game/Scenes/Main.unity
  artifactKey: Guid(80207517658dd27438d88dd267d39966) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main.unity using Guid(80207517658dd27438d88dd267d39966) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf6d6a8808cb2861cb5492d25f0649e5') in 0.0119447 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.369 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.04 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.965 seconds
Domain Reload Profiling: 4313ms
	BeginReloadAssembly (464ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (70ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (1756ms)
		LoadAssemblies (1299ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (630ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (585ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (1966ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1255ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (364ms)
			ProcessInitializeOnLoadAttributes (676ms)
			ProcessInitializeOnLoadMethodAttributes (154ms)
			AfterProcessingInitializeOnLoad (52ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (43ms)
Refreshing native plugins compatible for Editor in 299.16 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7978 unused Assets / (5.9 MB). Loaded Objects now: 9014.
Memory consumption went from 239.6 MB to 233.7 MB.
Total: 30.998700 ms (FindLiveObjects: 1.374600 ms CreateObjectMapping: 2.456200 ms MarkObjects: 21.319100 ms  DeleteObjects: 5.846800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.464 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.18 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.768 seconds
Domain Reload Profiling: 4151ms
	BeginReloadAssembly (826ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (70ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (107ms)
	LoadAllAssembliesAndSetupDomain (1378ms)
		LoadAssemblies (1469ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (443ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (398ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1769ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1447ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (548ms)
			ProcessInitializeOnLoadAttributes (640ms)
			ProcessInitializeOnLoadMethodAttributes (217ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (48ms)
Refreshing native plugins compatible for Editor in 25.36 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.4 MB). Loaded Objects now: 9016.
Memory consumption went from 239.6 MB to 233.2 MB.
Total: 15.386700 ms (FindLiveObjects: 1.545900 ms CreateObjectMapping: 1.663700 ms MarkObjects: 7.722700 ms  DeleteObjects: 4.451000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3025.918346 seconds.
  path: Assets/_Game/Scripts/Interface/Cursor/Cur1.png
  artifactKey: Guid(22a895c511976c94e9f7f131973321c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Cursor/Cur1.png using Guid(22a895c511976c94e9f7f131973321c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '906c9eb76f6f4f7f8049a616cd238af0') in 0.1580541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 85.899751 seconds.
  path: Assets/_Game/Shaders/JPEGCompressionShader
  artifactKey: Guid(8936c60fcbb4bd642bc6864fad1b090d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Shaders/JPEGCompressionShader using Guid(8936c60fcbb4bd642bc6864fad1b090d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5c154931f85ed1edb7991c1bd229e80d') in 0.0007356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 67.369937 seconds.
  path: Assets/_Game/Materials/GRIDPNG/Orange
  artifactKey: Guid(fb845b850b7961e4ea815377c800e6e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Materials/GRIDPNG/Orange using Guid(fb845b850b7961e4ea815377c800e6e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b16bbccb75834bddccaab91cb066646b') in 0.0007709 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.825086 seconds.
  path: Assets/_Game/Materials/GRIDPNG/Light/Materials
  artifactKey: Guid(704d736a61596e34c98f89b04167f00c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Materials/GRIDPNG/Light/Materials using Guid(704d736a61596e34c98f89b04167f00c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3735e3754583a5f85b20377e4270e732') in 0.0007659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.914 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.46 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.284 seconds
Domain Reload Profiling: 4123ms
	BeginReloadAssembly (543ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (47ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (134ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (80ms)
	LoadAllAssembliesAndSetupDomain (1144ms)
		LoadAssemblies (1001ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (360ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (314ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (2285ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1905ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (449ms)
			ProcessInitializeOnLoadAttributes (1150ms)
			ProcessInitializeOnLoadMethodAttributes (251ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (60ms)
Refreshing native plugins compatible for Editor in 27.66 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.4 MB). Loaded Objects now: 9020.
Memory consumption went from 239.7 MB to 234.3 MB.
Total: 19.363300 ms (FindLiveObjects: 1.392600 ms CreateObjectMapping: 1.715600 ms MarkObjects: 10.784100 ms  DeleteObjects: 5.469500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1701.851217 seconds.
  path: Assets/_Game/Scenes/Main.unity
  artifactKey: Guid(80207517658dd27438d88dd267d39966) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main.unity using Guid(80207517658dd27438d88dd267d39966) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc39512d492f343c8809aeecd2e75258') in 0.027525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.080 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.82 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.462 seconds
Domain Reload Profiling: 3497ms
	BeginReloadAssembly (482ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (62ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (76ms)
	LoadAllAssembliesAndSetupDomain (1403ms)
		LoadAssemblies (1156ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (410ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (361ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1463ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1095ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (354ms)
			ProcessInitializeOnLoadAttributes (569ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 21.81 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.8 MB). Loaded Objects now: 9022.
Memory consumption went from 239.8 MB to 233.0 MB.
Total: 13.294300 ms (FindLiveObjects: 0.900400 ms CreateObjectMapping: 1.224600 ms MarkObjects: 7.066700 ms  DeleteObjects: 4.101500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.715 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.57 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.886 seconds
Domain Reload Profiling: 3576ms
	BeginReloadAssembly (591ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (984ms)
		LoadAssemblies (907ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (306ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (267ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1533ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (365ms)
			ProcessInitializeOnLoadAttributes (901ms)
			ProcessInitializeOnLoadMethodAttributes (207ms)
			AfterProcessingInitializeOnLoad (47ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (57ms)
Refreshing native plugins compatible for Editor in 31.35 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.5 MB). Loaded Objects now: 9024.
Memory consumption went from 239.9 MB to 234.4 MB.
Total: 18.297100 ms (FindLiveObjects: 1.661900 ms CreateObjectMapping: 2.442900 ms MarkObjects: 10.321100 ms  DeleteObjects: 3.869200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1068.956161 seconds.
  path: Assets/_Game/Scripts/Interface/Menu/StartMenuManager.cs
  artifactKey: Guid(69cff9d095923014696214f99a1513ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Menu/StartMenuManager.cs using Guid(69cff9d095923014696214f99a1513ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a6f64855be60060834e20e535ec1a2e0') in 0.0310145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.979 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.48 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.541 seconds
Domain Reload Profiling: 3490ms
	BeginReloadAssembly (481ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1338ms)
		LoadAssemblies (1177ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (381ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (341ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1541ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1214ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (40ms)
			BeforeProcessingInitializeOnLoad (409ms)
			ProcessInitializeOnLoadAttributes (604ms)
			ProcessInitializeOnLoadMethodAttributes (134ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 21.01 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.5 MB). Loaded Objects now: 9026.
Memory consumption went from 240.0 MB to 233.6 MB.
Total: 13.758800 ms (FindLiveObjects: 1.025100 ms CreateObjectMapping: 0.951500 ms MarkObjects: 7.692100 ms  DeleteObjects: 4.088900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.692 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 68.61 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.708 seconds
Domain Reload Profiling: 4345ms
	BeginReloadAssembly (658ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (79ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (149ms)
	LoadAllAssembliesAndSetupDomain (1732ms)
		LoadAssemblies (1548ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (552ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (477ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (1710ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1349ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (439ms)
			ProcessInitializeOnLoadAttributes (692ms)
			ProcessInitializeOnLoadMethodAttributes (174ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (51ms)
Refreshing native plugins compatible for Editor in 30.67 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.4 MB). Loaded Objects now: 9028.
Memory consumption went from 240.1 MB to 233.7 MB.
Total: 18.461500 ms (FindLiveObjects: 1.383900 ms CreateObjectMapping: 2.454200 ms MarkObjects: 9.016400 ms  DeleteObjects: 5.606100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.358 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 128.00 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.616 seconds
Domain Reload Profiling: 4922ms
	BeginReloadAssembly (535ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (70ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (138ms)
	LoadAllAssembliesAndSetupDomain (1563ms)
		LoadAssemblies (1363ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (433ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (387ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (2616ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2244ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (598ms)
			ProcessInitializeOnLoadAttributes (1329ms)
			ProcessInitializeOnLoadMethodAttributes (215ms)
			AfterProcessingInitializeOnLoad (91ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (103ms)
Refreshing native plugins compatible for Editor in 35.88 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (7.6 MB). Loaded Objects now: 9030.
Memory consumption went from 240.2 MB to 232.7 MB.
Total: 48.168000 ms (FindLiveObjects: 1.343000 ms CreateObjectMapping: 1.758700 ms MarkObjects: 38.006500 ms  DeleteObjects: 7.057800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.034 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 44.37 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.465 seconds
Domain Reload Profiling: 3465ms
	BeginReloadAssembly (558ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (60ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (86ms)
	LoadAllAssembliesAndSetupDomain (1262ms)
		LoadAssemblies (1118ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (404ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (360ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1466ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1167ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (371ms)
			ProcessInitializeOnLoadAttributes (618ms)
			ProcessInitializeOnLoadMethodAttributes (136ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 18.00 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.8 MB). Loaded Objects now: 9032.
Memory consumption went from 240.3 MB to 234.5 MB.
Total: 11.387400 ms (FindLiveObjects: 0.783600 ms CreateObjectMapping: 0.530400 ms MarkObjects: 6.749400 ms  DeleteObjects: 3.322900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.049 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 127.89 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.187 seconds
Domain Reload Profiling: 4202ms
	BeginReloadAssembly (616ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (88ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (108ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (283ms)
	LoadAllAssembliesAndSetupDomain (1046ms)
		LoadAssemblies (929ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (346ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (310ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (2187ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1766ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (486ms)
			ProcessInitializeOnLoadAttributes (1082ms)
			ProcessInitializeOnLoadMethodAttributes (151ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (91ms)
Refreshing native plugins compatible for Editor in 26.50 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (7.2 MB). Loaded Objects now: 9034.
Memory consumption went from 240.4 MB to 233.2 MB.
Total: 16.428600 ms (FindLiveObjects: 1.524200 ms CreateObjectMapping: 1.546900 ms MarkObjects: 8.161600 ms  DeleteObjects: 5.194000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 442.67 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7925 unused Assets / (6.0 MB). Loaded Objects now: 9034.
Memory consumption went from 240.3 MB to 234.4 MB.
Total: 13.092500 ms (FindLiveObjects: 1.224300 ms CreateObjectMapping: 0.751900 ms MarkObjects: 7.068700 ms  DeleteObjects: 4.046300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.484 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 44.50 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.134 seconds
Domain Reload Profiling: 7570ms
	BeginReloadAssembly (831ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (46ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (216ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (96ms)
	LoadAllAssembliesAndSetupDomain (4270ms)
		LoadAssemblies (3991ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (858ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (815ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (2135ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1674ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (31ms)
			BeforeProcessingInitializeOnLoad (413ms)
			ProcessInitializeOnLoadAttributes (1029ms)
			ProcessInitializeOnLoadMethodAttributes (167ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (51ms)
Refreshing native plugins compatible for Editor in 26.44 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.8 MB). Loaded Objects now: 9036.
Memory consumption went from 240.5 MB to 234.7 MB.
Total: 13.432600 ms (FindLiveObjects: 1.234000 ms CreateObjectMapping: 1.081900 ms MarkObjects: 7.282500 ms  DeleteObjects: 3.833100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.645 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 19.49 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.220 seconds
Domain Reload Profiling: 2846ms
	BeginReloadAssembly (495ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (106ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (1013ms)
		LoadAssemblies (884ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (326ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (278ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1220ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (923ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (248ms)
			ProcessInitializeOnLoadAttributes (504ms)
			ProcessInitializeOnLoadMethodAttributes (134ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (60ms)
Refreshing native plugins compatible for Editor in 23.36 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.4 MB). Loaded Objects now: 9038.
Memory consumption went from 240.6 MB to 234.2 MB.
Total: 15.582100 ms (FindLiveObjects: 1.341800 ms CreateObjectMapping: 1.589800 ms MarkObjects: 8.412700 ms  DeleteObjects: 4.236600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.430 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 34.47 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.827 seconds
Domain Reload Profiling: 4226ms
	BeginReloadAssembly (645ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (85ms)
	initialDomainReloadingComplete (72ms)
	LoadAllAssembliesAndSetupDomain (1523ms)
		LoadAssemblies (1447ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (431ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (386ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1827ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1421ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (564ms)
			ProcessInitializeOnLoadAttributes (666ms)
			ProcessInitializeOnLoadMethodAttributes (145ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (58ms)
Refreshing native plugins compatible for Editor in 16.06 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.9 MB). Loaded Objects now: 9040.
Memory consumption went from 240.7 MB to 233.9 MB.
Total: 12.912700 ms (FindLiveObjects: 0.752500 ms CreateObjectMapping: 0.945000 ms MarkObjects: 7.149800 ms  DeleteObjects: 4.064600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.158 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 38.50 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.550 seconds
Domain Reload Profiling: 3642ms
	BeginReloadAssembly (426ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (86ms)
	LoadAllAssembliesAndSetupDomain (1515ms)
		LoadAssemblies (1194ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (508ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (14ms)
			BuildScriptInfoCaches (455ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1551ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1211ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (324ms)
			ProcessInitializeOnLoadAttributes (693ms)
			ProcessInitializeOnLoadMethodAttributes (145ms)
			AfterProcessingInitializeOnLoad (39ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (61ms)
Refreshing native plugins compatible for Editor in 36.84 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.2 MB). Loaded Objects now: 9042.
Memory consumption went from 240.9 MB to 234.6 MB.
Total: 14.365200 ms (FindLiveObjects: 1.465700 ms CreateObjectMapping: 1.455300 ms MarkObjects: 7.470700 ms  DeleteObjects: 3.972400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.437 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 20.20 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.409 seconds
Domain Reload Profiling: 4819ms
	BeginReloadAssembly (2136ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (72ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (858ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (91ms)
	LoadAllAssembliesAndSetupDomain (1094ms)
		LoadAssemblies (1300ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (369ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (325ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1410ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1128ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (290ms)
			ProcessInitializeOnLoadAttributes (579ms)
			ProcessInitializeOnLoadMethodAttributes (189ms)
			AfterProcessingInitializeOnLoad (61ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (49ms)
Refreshing native plugins compatible for Editor in 23.39 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.6 MB). Loaded Objects now: 9044.
Memory consumption went from 241.0 MB to 234.4 MB.
Total: 13.864900 ms (FindLiveObjects: 0.774800 ms CreateObjectMapping: 1.424400 ms MarkObjects: 7.233200 ms  DeleteObjects: 4.431300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.893 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.67 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.111 seconds
Domain Reload Profiling: 4934ms
	BeginReloadAssembly (760ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (78ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (287ms)
	LoadAllAssembliesAndSetupDomain (1681ms)
		LoadAssemblies (1618ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (489ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (439ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (2112ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1603ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (628ms)
			ProcessInitializeOnLoadAttributes (673ms)
			ProcessInitializeOnLoadMethodAttributes (250ms)
			AfterProcessingInitializeOnLoad (38ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (56ms)
Refreshing native plugins compatible for Editor in 46.23 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.4 MB). Loaded Objects now: 9046.
Memory consumption went from 241.1 MB to 235.7 MB.
Total: 15.918400 ms (FindLiveObjects: 1.609000 ms CreateObjectMapping: 1.801900 ms MarkObjects: 8.715200 ms  DeleteObjects: 3.790500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.259 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.80 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.772 seconds
Domain Reload Profiling: 4000ms
	BeginReloadAssembly (528ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (1557ms)
		LoadAssemblies (1393ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (426ms)
			TypeCache.Refresh (56ms)
				TypeCache.ScanAssembly (20ms)
			BuildScriptInfoCaches (344ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1772ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1296ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (410ms)
			ProcessInitializeOnLoadAttributes (668ms)
			ProcessInitializeOnLoadMethodAttributes (182ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (49ms)
Refreshing native plugins compatible for Editor in 19.75 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7975 unused Assets / (6.5 MB). Loaded Objects now: 9049.
Memory consumption went from 241.2 MB to 234.6 MB.
Total: 16.284900 ms (FindLiveObjects: 1.180200 ms CreateObjectMapping: 1.465400 ms MarkObjects: 8.134100 ms  DeleteObjects: 5.503900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7233.681665 seconds.
  path: Assets/_Game/Scenes/Main/Abyss Volume Profile.asset
  artifactKey: Guid(d93be83ba4fe8c04d80b4a0b09c193b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Abyss Volume Profile.asset using Guid(d93be83ba4fe8c04d80b4a0b09c193b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20e4040a39ec755f77764f3b45284d59') in 0.9666776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-0_comp_light.exr
  artifactKey: Guid(d73695f6ad8f49647bf017a85a954add) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-0_comp_light.exr using Guid(d73695f6ad8f49647bf017a85a954add) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54acde2d974f3ccbf0887cbc528721f1') in 0.0924577 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-3_comp_light.exr
  artifactKey: Guid(a091bfea634e71241bdb2c973068549b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-3_comp_light.exr using Guid(a091bfea634e71241bdb2c973068549b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4071aaa06f4020d8d3ca8a3d4efaa1c1') in 0.0236177 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_Game/Scenes/Main/IndoorsSceneProfile 1.asset
  artifactKey: Guid(7b5692f9d4b2d0f46a23e3a047918f9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/IndoorsSceneProfile 1.asset using Guid(7b5692f9d4b2d0f46a23e3a047918f9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '44583a342bd0bd1650354e7f0ff5358a') in 0.0025793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-1_comp_dir.png
  artifactKey: Guid(48cf0ac80f5b3374792a7f3a347d9c2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-1_comp_dir.png using Guid(48cf0ac80f5b3374792a7f3a347d9c2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '30aa229011a99ec70c2709d043b0eec0') in 0.0312767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_Game/Scenes/Main/LightingData.asset
  artifactKey: Guid(f42ea30a6c43fbf44b01bf37d169a110) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/LightingData.asset using Guid(f42ea30a6c43fbf44b01bf37d169a110) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5e6e92f5dd2a3ede09cc965ed680778f') in 0.0025694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-2_comp_light.exr
  artifactKey: Guid(949e5e15ac926e4418ee8c1d4b120ef0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-2_comp_light.exr using Guid(949e5e15ac926e4418ee8c1d4b120ef0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23d81bb1558a242800ac262901a42b0f') in 0.0204265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-1_comp_light.exr
  artifactKey: Guid(f03f6643f1b6ab24ca55a81b2242a6c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-1_comp_light.exr using Guid(f03f6643f1b6ab24ca55a81b2242a6c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '71135ed7831b87541b019fded4a17977') in 0.0140356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-4_comp_light.exr
  artifactKey: Guid(94568b4bc3f94b7468c7655b7119d20d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-4_comp_light.exr using Guid(94568b4bc3f94b7468c7655b7119d20d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '99672fed191fcfe786606395f3544c41') in 0.0558104 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_Game/Scenes/Main/Indoors RayTracing Profile.asset
  artifactKey: Guid(92947c352b5315c4387a650079fd0b1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Indoors RayTracing Profile.asset using Guid(92947c352b5315c4387a650079fd0b1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9d9da4c1b0b7df3216c8b28d3cb21650') in 0.0031145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-4_comp_dir.png
  artifactKey: Guid(f5719cd90eef0014f9c123196cb4c931) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-4_comp_dir.png using Guid(f5719cd90eef0014f9c123196cb4c931) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9f636f4921708676f0ee4934170d95a4') in 0.0560393 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.47 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7927 unused Assets / (6.1 MB). Loaded Objects now: 9053.
Memory consumption went from 241.6 MB to 235.5 MB.
Total: 27.506400 ms (FindLiveObjects: 2.320300 ms CreateObjectMapping: 1.447500 ms MarkObjects: 19.006700 ms  DeleteObjects: 4.730500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.037 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.06 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.249 seconds
Domain Reload Profiling: 3259ms
	BeginReloadAssembly (498ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (46ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (105ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (1367ms)
		LoadAssemblies (1190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (389ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (348ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1250ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (978ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (269ms)
			ProcessInitializeOnLoadAttributes (544ms)
			ProcessInitializeOnLoadMethodAttributes (133ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 17.09 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.4 MB). Loaded Objects now: 9055.
Memory consumption went from 241.3 MB to 235.0 MB.
Total: 12.825700 ms (FindLiveObjects: 1.188200 ms CreateObjectMapping: 0.613300 ms MarkObjects: 7.391400 ms  DeleteObjects: 3.631700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.014 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 36.09 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.258 seconds
Domain Reload Profiling: 3241ms
	BeginReloadAssembly (538ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (127ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (1304ms)
		LoadAssemblies (1101ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (414ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (364ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1258ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (984ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (284ms)
			ProcessInitializeOnLoadAttributes (532ms)
			ProcessInitializeOnLoadMethodAttributes (129ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 19.08 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.6 MB). Loaded Objects now: 9057.
Memory consumption went from 241.5 MB to 234.9 MB.
Total: 11.079300 ms (FindLiveObjects: 1.031000 ms CreateObjectMapping: 0.920000 ms MarkObjects: 5.068200 ms  DeleteObjects: 4.058900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2196.613129 seconds.
  path: Assets/_Game/Prefabs/DroppedEquipment.prefab
  artifactKey: Guid(a78024b566c5689478acecfac2568510) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Prefabs/DroppedEquipment.prefab using Guid(a78024b566c5689478acecfac2568510) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61cf075018a5497093c949167675bf65') in 1.3912018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_Game/Resources/Items
  artifactKey: Guid(9dd58479075a77546a4dcd0b1e6f4d8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items using Guid(9dd58479075a77546a4dcd0b1e6f4d8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9458e9d09b595fbff079eeedc9cd0a8e') in 0.0011629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/_Game/Resources/Items/Rope.asset
  artifactKey: Guid(76e4408dc4a8b0b4eba1569e37d14faf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/Rope.asset using Guid(76e4408dc4a8b0b4eba1569e37d14faf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1df8a1d86b917f40e2b8ac90d4c1114c') in 0.0053827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Resources/Items/Helmet.asset
  artifactKey: Guid(9d9e72b8ea5471c468537456f5a50581) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/Helmet.asset using Guid(9d9e72b8ea5471c468537456f5a50581) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf7b5d541680dab760205f28b468543b') in 0.0021577 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/_Game/Resources/Items/ExoSuite.asset
  artifactKey: Guid(9ce720979c2abd144b16fb563894c2aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/ExoSuite.asset using Guid(9ce720979c2abd144b16fb563894c2aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '70953421620ebe73a716d6b5bacc1ccc') in 0.0021227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_Game/Resources/Items/NewTool.asset
  artifactKey: Guid(a48b1fe7d1e7c37438e4246f456af1bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/NewTool.asset using Guid(a48b1fe7d1e7c37438e4246f456af1bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '217fad022f957fa706496335967f04d2') in 0.0046095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Leak Detected : Persistent allocates 8 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.337 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 28.94 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.030 seconds
Domain Reload Profiling: 5320ms
	BeginReloadAssembly (1051ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (118ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (245ms)
	RebuildCommonClasses (85ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (105ms)
	LoadAllAssembliesAndSetupDomain (2027ms)
		LoadAssemblies (1948ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (451ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (412ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (2030ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1622ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (462ms)
			ProcessInitializeOnLoadAttributes (877ms)
			ProcessInitializeOnLoadMethodAttributes (224ms)
			AfterProcessingInitializeOnLoad (47ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (50ms)
Refreshing native plugins compatible for Editor in 35.46 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7977 unused Assets / (6.9 MB). Loaded Objects now: 9136.
Memory consumption went from 246.6 MB to 239.7 MB.
Total: 31.359600 ms (FindLiveObjects: 3.873600 ms CreateObjectMapping: 2.455500 ms MarkObjects: 16.687500 ms  DeleteObjects: 8.341400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.765 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.08 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.475 seconds
Domain Reload Profiling: 3213ms
	BeginReloadAssembly (435ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (84ms)
	LoadAllAssembliesAndSetupDomain (1145ms)
		LoadAssemblies (981ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (344ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (307ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1476ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1140ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (407ms)
			ProcessInitializeOnLoadAttributes (544ms)
			ProcessInitializeOnLoadMethodAttributes (154ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 30.45 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (3.9 MB). Loaded Objects now: 9138.
Memory consumption went from 246.4 MB to 242.5 MB.
Total: 18.640600 ms (FindLiveObjects: 1.967000 ms CreateObjectMapping: 1.991700 ms MarkObjects: 10.807000 ms  DeleteObjects: 3.873500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.628 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.15 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.562 seconds
Domain Reload Profiling: 3165ms
	BeginReloadAssembly (414ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (1067ms)
		LoadAssemblies (883ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (349ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (307ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1562ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1280ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (424ms)
			ProcessInitializeOnLoadAttributes (630ms)
			ProcessInitializeOnLoadMethodAttributes (186ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (49ms)
Refreshing native plugins compatible for Editor in 28.20 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.4 MB). Loaded Objects now: 9140.
Memory consumption went from 246.5 MB to 240.1 MB.
Total: 28.652000 ms (FindLiveObjects: 1.357000 ms CreateObjectMapping: 2.087400 ms MarkObjects: 20.520400 ms  DeleteObjects: 4.685600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.974 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 27.69 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.473 seconds
Domain Reload Profiling: 4428ms
	BeginReloadAssembly (673ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (92ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (111ms)
	LoadAllAssembliesAndSetupDomain (2049ms)
		LoadAssemblies (1643ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (739ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (659ms)
			ResolveRequiredComponents (41ms)
	FinalizeReload (1474ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1164ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (342ms)
			ProcessInitializeOnLoadAttributes (623ms)
			ProcessInitializeOnLoadMethodAttributes (157ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 19.14 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.1 MB). Loaded Objects now: 9142.
Memory consumption went from 246.6 MB to 240.5 MB.
Total: 13.928500 ms (FindLiveObjects: 0.806600 ms CreateObjectMapping: 0.979700 ms MarkObjects: 8.151400 ms  DeleteObjects: 3.989800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.428 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 32.87 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.919 seconds
Domain Reload Profiling: 4300ms
	BeginReloadAssembly (524ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (117ms)
	LoadAllAssembliesAndSetupDomain (1643ms)
		LoadAssemblies (1381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (451ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (22ms)
			BuildScriptInfoCaches (388ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1920ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1434ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (24ms)
			BeforeProcessingInitializeOnLoad (406ms)
			ProcessInitializeOnLoadAttributes (766ms)
			ProcessInitializeOnLoadMethodAttributes (200ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (55ms)
Refreshing native plugins compatible for Editor in 25.82 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.1 MB). Loaded Objects now: 9144.
Memory consumption went from 246.7 MB to 240.6 MB.
Total: 16.441000 ms (FindLiveObjects: 1.676100 ms CreateObjectMapping: 1.734500 ms MarkObjects: 7.900300 ms  DeleteObjects: 5.128600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.230 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 49.24 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.190 seconds
Domain Reload Profiling: 5389ms
	BeginReloadAssembly (1852ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (247ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (811ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (1177ms)
		LoadAssemblies (1013ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (364ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (327ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (2191ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1808ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (588ms)
			ProcessInitializeOnLoadAttributes (900ms)
			ProcessInitializeOnLoadMethodAttributes (254ms)
			AfterProcessingInitializeOnLoad (54ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (65ms)
Refreshing native plugins compatible for Editor in 53.02 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (4.9 MB). Loaded Objects now: 9146.
Memory consumption went from 246.8 MB to 241.9 MB.
Total: 34.421000 ms (FindLiveObjects: 10.057200 ms CreateObjectMapping: 2.114900 ms MarkObjects: 13.490000 ms  DeleteObjects: 8.757400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.236 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.62 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.452 seconds
Domain Reload Profiling: 3656ms
	BeginReloadAssembly (783ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (160ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (89ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (1250ms)
		LoadAssemblies (1129ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (387ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (347ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1453ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1074ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (293ms)
			ProcessInitializeOnLoadAttributes (590ms)
			ProcessInitializeOnLoadMethodAttributes (155ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (49ms)
Refreshing native plugins compatible for Editor in 24.10 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.0 MB). Loaded Objects now: 9148.
Memory consumption went from 246.9 MB to 240.9 MB.
Total: 15.685500 ms (FindLiveObjects: 1.232800 ms CreateObjectMapping: 1.687900 ms MarkObjects: 7.820300 ms  DeleteObjects: 4.943300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.680 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 41.99 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.110 seconds
Domain Reload Profiling: 5692ms
	BeginReloadAssembly (1587ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (332ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (125ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (248ms)
	LoadAllAssembliesAndSetupDomain (1597ms)
		LoadAssemblies (2091ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (327ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (290ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (2111ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1232ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (338ms)
			ProcessInitializeOnLoadAttributes (665ms)
			ProcessInitializeOnLoadMethodAttributes (184ms)
			AfterProcessingInitializeOnLoad (35ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (100ms)
Refreshing native plugins compatible for Editor in 24.06 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (5.5 MB). Loaded Objects now: 9150.
Memory consumption went from 247.0 MB to 241.5 MB.
Total: 19.917400 ms (FindLiveObjects: 1.360900 ms CreateObjectMapping: 2.093000 ms MarkObjects: 8.932100 ms  DeleteObjects: 7.530100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.32 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7927 unused Assets / (5.8 MB). Loaded Objects now: 9150.
Memory consumption went from 246.9 MB to 241.1 MB.
Total: 192.724900 ms (FindLiveObjects: 1.409400 ms CreateObjectMapping: 0.990900 ms MarkObjects: 186.593500 ms  DeleteObjects: 3.729500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 27.64 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7927 unused Assets / (6.5 MB). Loaded Objects now: 9150.
Memory consumption went from 246.9 MB to 240.5 MB.
Total: 18.147100 ms (FindLiveObjects: 1.255600 ms CreateObjectMapping: 1.626700 ms MarkObjects: 10.737300 ms  DeleteObjects: 4.526300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.088 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.75 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.356 seconds
Domain Reload Profiling: 7407ms
	BeginReloadAssembly (1206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (115ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (253ms)
	RebuildCommonClasses (260ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (83ms)
	LoadAllAssembliesAndSetupDomain (4487ms)
		LoadAssemblies (4346ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (801ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (755ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1356ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1030ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (291ms)
			ProcessInitializeOnLoadAttributes (572ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (43ms)
Refreshing native plugins compatible for Editor in 23.91 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.9 MB). Loaded Objects now: 9152.
Memory consumption went from 247.1 MB to 240.2 MB.
Total: 12.792500 ms (FindLiveObjects: 1.235000 ms CreateObjectMapping: 0.993500 ms MarkObjects: 5.292700 ms  DeleteObjects: 5.270500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.825 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.30 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.832 seconds
Domain Reload Profiling: 3555ms
	BeginReloadAssembly (444ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (39ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (87ms)
	LoadAllAssembliesAndSetupDomain (1122ms)
		LoadAssemblies (952ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (366ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (326ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1833ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (446ms)
			ProcessInitializeOnLoadAttributes (696ms)
			ProcessInitializeOnLoadMethodAttributes (315ms)
			AfterProcessingInitializeOnLoad (37ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (66ms)
Refreshing native plugins compatible for Editor in 18.41 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.3 MB). Loaded Objects now: 9154.
Memory consumption went from 247.2 MB to 240.9 MB.
Total: 15.317700 ms (FindLiveObjects: 0.776300 ms CreateObjectMapping: 1.411700 ms MarkObjects: 8.525700 ms  DeleteObjects: 4.602600 ms)

Prepare: number of updated asset objects reloaded= 0
