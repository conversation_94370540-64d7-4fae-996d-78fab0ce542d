using UnityEngine;
using KinematicCharacterController.FPS;

public class InteractionManager : MonoBehaviour
{
    [<PERSON><PERSON>("Interaction Settings")]
    [Tooltip("Maximum distance at which the player can interact with objects.")]
    [SerializeField] private float maxInteractionDistance = 3f;

    [Tooltip("Key to press for interacting.")]
    [SerializeField] private KeyCode interactKey = KeyCode.F;

    [<PERSON><PERSON>("Optimization Settings")]
    [Tooltip("How often to update interaction highlighting (in seconds)")]
    [SerializeField] private float raycastUpdateInterval = 0.1f;
    
    [Tooltip("Minimum camera movement before forcing a raycast update")]
    [SerializeField] private float cameraMovementThreshold = 0.01f;
    
    [Tooltip("Minimum camera rotation before forcing a raycast update (degrees)")]
    [SerializeField] private float cameraRotationThreshold = 1f;

    [Header("References")]
    [Tooltip("Reference to the player's camera component.")]
    [SerializeField] private FPSCharacterCamera cameraController;
    
    [Toolt<PERSON>("Reference to the player's main camera.")]
    [SerializeField] private Camera playerCamera;
    
    [<PERSON><PERSON><PERSON>("Reference to the grab interaction component")]
    [SerializeField] private GrabInteraction grabInteraction;

    // Current highlighted interactable
    private IInteractable currentInteractable;
    
    // Track key press timing
    private float keyPressTime = 0f;
    private bool waitingForKeyRelease = false;
    
    // Raycast optimization
    private float lastRaycastTime = 0f;
    private Vector3 lastCameraPosition;
    private Quaternion lastCameraRotation;
    
    // Cached raycast results
    private RaycastHit cachedHitInfo;
    private bool hasCachedHit = false;
    private float cacheTime = 0f;
    private const float CACHE_DURATION = 0.05f; // Cache results for 50ms

    // Hold-to-interact state (e.g., hinge drag)
    private bool holdActive = false;
    private IHoldInteractable currentHoldTarget;

    private void Awake()
    {
        // Make sure the GrabInteraction component exists
        if (grabInteraction == null)
        {
            grabInteraction = GetComponent<GrabInteraction>();
            if (grabInteraction == null)
            {
                grabInteraction = gameObject.AddComponent<GrabInteraction>();
                Debug.Log("Added GrabInteraction component");
            }
        }
    }

    private void Start()
    {
        // If camera controller not set, try to find it
        if (cameraController == null)
        {
            cameraController = FindObjectOfType<FPSCharacterCamera>();
            if (cameraController != null)
            {
                playerCamera = cameraController.Camera;
            }
        }
        
        // If we still don't have the player camera, try to find it directly
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
            Debug.LogWarning("Player camera not assigned. Using Camera.main as fallback.");
        }
        
        // Initialize camera tracking
        if (playerCamera != null)
        {
            lastCameraPosition = playerCamera.transform.position;
            lastCameraRotation = playerCamera.transform.rotation;
        }
        
        // Initialize the grab interaction with the camera
        if (grabInteraction != null && playerCamera != null)
        {
            grabInteraction.Initialize(playerCamera);
            Debug.Log($"Initialized GrabInteraction with camera: {playerCamera.name}");
        }
        else
        {
            Debug.LogError("Failed to initialize GrabInteraction: " + 
                          (grabInteraction == null ? "component is null" : "camera is null"));
        }
    }

    private void Update()
    {
        if (playerCamera == null) return;
        
        // If a hold-driven interactable is active, route input to it and skip normal flow
        if (holdActive)
        {
            if (currentHoldTarget == null)
            {
                EndHold(false);
                return;
            }

            // Feed mouse deltas while key is held
            if (Input.GetKey(interactKey))
            {
                float dx = Input.GetAxis("Mouse X");
                float dy = Input.GetAxis("Mouse Y");
                currentHoldTarget.OnHold(gameObject, dx, dy);
            }

            // End hold on key up
            if (Input.GetKeyUp(interactKey))
            {
                EndHold(false);
            }
            return;
        }

        // If grab system is active, let it handle everything
        if (grabInteraction.IsGrabbing) 
        {
            return;
        }

        // Handle key press for interaction vs. grab
        if (Input.GetKeyDown(interactKey))
        {
            keyPressTime = Time.time;
            waitingForKeyRelease = true;
        }
        
        // If key is held long enough, start hold if supported, otherwise try to grab
        if (waitingForKeyRelease && Input.GetKey(interactKey))
        {
            if (Time.time - keyPressTime > grabInteraction.GrabHoldTime && !grabInteraction.IsGrabbing)
            {
                // Prefer hold-enabled interactables over grab when available
                IHoldInteractable holdCandidate = currentInteractable as IHoldInteractable;
                if (holdCandidate != null && holdCandidate.WantsHold(gameObject))
                {
                    StartHold(holdCandidate);
                    waitingForKeyRelease = false;
                }
                else
                {
                    // Try to grab instead of interact
                    grabInteraction.AttemptGrab();
                    waitingForKeyRelease = false; // Handled, no longer waiting
                }
            }
        }
        
        // If key is released before grab threshold, handle as normal interaction
        if (waitingForKeyRelease && Input.GetKeyUp(interactKey))
        {
            waitingForKeyRelease = false;
            // Only interact if we didn't start grabbing
            if (!grabInteraction.IsGrabbing)
            {
                AttemptInteraction();
            }
        }
        
        // Update highlighting with optimized raycasting
        if (ShouldUpdateRaycast())
        {
            UpdateInteractionHighlight();
        }
    }

    private void StartHold(IHoldInteractable target)
    {
        currentHoldTarget = target;
        holdActive = true;
        currentHoldTarget.OnHoldStarted(gameObject);
        if (cameraController != null)
        {
            cameraController.SetGrabRotationMode(true);
        }
    }

    private void EndHold(bool canceled)
    {
        if (!holdActive) return;
        try
        {
            currentHoldTarget?.OnHoldEnded(gameObject, canceled);
        }
        finally
        {
            holdActive = false;
            currentHoldTarget = null;
            if (cameraController != null)
            {
                cameraController.SetGrabRotationMode(false);
            }
        }
    }

    /// <summary>
    /// Determines if we should perform a new raycast based on time and camera movement
    /// </summary>
    private bool ShouldUpdateRaycast()
    {
        // Check if enough time has passed since last raycast
        float timeSinceLastRaycast = Time.time - lastRaycastTime;
        bool timeThresholdMet = timeSinceLastRaycast >= raycastUpdateInterval;
        
        // Check if camera has moved significantly
        float positionDelta = Vector3.Distance(playerCamera.transform.position, lastCameraPosition);
        bool positionThresholdMet = positionDelta > cameraMovementThreshold;
        
        // Check if camera has rotated significantly
        float rotationDelta = Quaternion.Angle(playerCamera.transform.rotation, lastCameraRotation);
        bool rotationThresholdMet = rotationDelta > cameraRotationThreshold;
        
        // Update if any threshold is met
        return timeThresholdMet || positionThresholdMet || rotationThresholdMet;
    }

    /// <summary>
    /// Updates which object is currently highlighted for interaction
    /// </summary>
    private void UpdateInteractionHighlight()
    {
        // Update tracking variables
        lastRaycastTime = Time.time;
        lastCameraPosition = playerCamera.transform.position;
        lastCameraRotation = playerCamera.transform.rotation;
        
        // Perform the raycast
        PerformInteractionRaycast();
        
        // Process the raycast result
        IInteractable newInteractable = null;
        if (hasCachedHit)
        {
            IInteractable interactable = cachedHitInfo.collider.GetComponent<IInteractable>();
            if (interactable != null && interactable.CanInteract(gameObject))
            {
                newInteractable = interactable;
            }
        }
        
        // Update highlighting if the interactable has changed
        if (newInteractable != currentInteractable)
        {
            // Remove highlight from previous interactable
            if (currentInteractable != null)
            {
                currentInteractable.RemoveHighlight();
            }
            
            // Add highlight to new interactable
            currentInteractable = newInteractable;
            if (currentInteractable != null)
            {
                currentInteractable.Highlight();
            }
        }
    }

    /// <summary>
    /// Performs a raycast and caches the result
    /// </summary>
    private void PerformInteractionRaycast()
    {
        // Check if we have a valid cached result
        if (Time.time - cacheTime < CACHE_DURATION && hasCachedHit)
        {
            return; // Use cached result
        }
        
        // Perform new raycast
        Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
        hasCachedHit = Physics.Raycast(ray, out cachedHitInfo, maxInteractionDistance);
        cacheTime = Time.time;
    }

    /// <summary>
    /// Attempts to interact with an object the player is looking at.
    /// </summary>
    private void AttemptInteraction()
    {
        // Use cached raycast result if it's recent enough
        if (Time.time - cacheTime < CACHE_DURATION && hasCachedHit)
        {
            IInteractable interactable = cachedHitInfo.collider.GetComponent<IInteractable>();
            if (interactable != null && interactable.CanInteract(gameObject))
            {
                interactable.Interact(gameObject);
            }
            return;
        }
        
        // Otherwise perform a fresh raycast
        Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
        RaycastHit hitInfo;

        if (Physics.Raycast(ray, out hitInfo, maxInteractionDistance))
        {
            // Check if the hit object has an IInteractable component
            IInteractable interactable = hitInfo.collider.GetComponent<IInteractable>();
            if (interactable != null && interactable.CanInteract(gameObject))
            {
                // Interact with the object directly
                interactable.Interact(gameObject);
            }
        }
    }
    
    /// <summary>
    /// Returns the current interactable being highlighted
    /// </summary>
    /// <returns>The current interactable, or null if none</returns>
    public IInteractable GetCurrentInteractable()
    {
        return currentInteractable;
    }
}