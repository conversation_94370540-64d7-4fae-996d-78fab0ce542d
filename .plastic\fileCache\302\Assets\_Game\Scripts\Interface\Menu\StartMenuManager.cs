using UnityEngine;
using UnityEngine.UIElements;
using KinematicCharacterController.FPS;
using System.Collections.Generic;
using System.Linq;

public class StartMenuManager : BaseMenuManager
{
    // Store the original title text to restore it later
    private string originalTitle = "";

    [Header("Overlay Mode (Main Scene)")]
    [SerializeField] private bool pauseGameWhileMenuOpen = true;

    [Tooltip("Player camera that will be enabled when continuing")] 
    [SerializeField] private Camera playerCamera;
    [SerializeField] private AudioListener playerAudioListener;
    [<PERSON><PERSON><PERSON>("Player FPS camera controller that handles mouse look")] 
    [SerializeField] private FPSCharacterCamera playerFpsCamera;
    [<PERSON><PERSON><PERSON>("Player character controller for movement")] 
    [SerializeField] private FPSCharacterController playerController;
    [<PERSON><PERSON><PERSON>("Player manager that may enforce cursor lock")] 
    [SerializeField] private FPSPlayerManager playerManager;
    [Tooltip("Inventory UI that may enforce cursor state")] 
    [SerializeField] private InvUI inventoryUI;
    [Toolt<PERSON>("Optional: Pause menu manager to disable E<PERSON> while start menu is open")] 
    [SerializeField] private PauseMenuManager pauseMenuManager;
    [<PERSON>lt<PERSON>("Central UI panel manager for inventory/stash/shop")] 
    [SerializeField] private UIPanelManager uiPanelManager;

    [Header("Start Menu Cameras")]
    [Tooltip("List of candidate menu cameras to choose from. Must have at least one camera.")]
    [SerializeField] private List<Camera> candidateMenuCameras = new List<Camera>();
    [Tooltip("How often to refresh the preferred menu camera based on player proximity (seconds)")]
    [SerializeField] private float cameraPreferenceUpdateIntervalSeconds = 180f;

    [Header("Fade In")] 
    [SerializeField] private bool enableFadeIn = true;
    [SerializeField] private float fadeInDuration = 0.6f;

    private VisualElement fadeOverlay;
    private const string MENU_CAMERA_PREF_KEY = "preferred_menu_camera_key";
    private static bool _hasSelectedCameraThisSession = false;

    private bool overlayModeActive = false;
    private Camera activeMenuCamera = null;

#if UNITY_EDITOR
    [UnityEditor.InitializeOnLoadMethod]
    private static void ResetSessionFlagOnPlayModeChange()
    {
        UnityEditor.EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
    }

    private static void OnPlayModeStateChanged(UnityEditor.PlayModeStateChange state)
    {
        if (state == UnityEditor.PlayModeStateChange.EnteredEditMode ||
            state == UnityEditor.PlayModeStateChange.EnteredPlayMode)
        {
            _hasSelectedCameraThisSession = false;
        }
    }
#endif

    protected override void Awake()
    {
        base.Awake();
        ValidateRequiredComponents();
    }

    protected override void Start()
    {
        base.Start();
        
        // Store the original title text
        if (titleLabel != null)
        {
            originalTitle = titleLabel.text;
        }
        
        SetupStartMenuButtons();

        UnityEngine.Cursor.visible = true;
        UnityEngine.Cursor.lockState = CursorLockMode.None;

        // Show main menu buttons
        ShowStartMenu();

        // Re-apply all settings on entering Start Menu scene
        if (SettingsManager.Instance != null)
        {
            SettingsManager.Instance.ApplyAllSettings();
        }

        // If we are placed inside the Main scene with player refs assigned, run in overlay mode
        TryAutoAssignPlayerRefsIfMissing();
        if (IsOverlaySetupPresent())
        {
            SelectAndActivateMenuCamera();
            ActivateOverlayMode();
            if (enableFadeIn)
            {
                StartCoroutine(FadeInFromBlack());
            }
        }

        // Start background updater to refresh preferred camera periodically during gameplay
        if (cameraPreferenceUpdateIntervalSeconds > 0f)
        {
            StartCoroutine(BackgroundPreferredCameraUpdater());
        }
    }

    protected override void InitializeUIElements()
    {
        base.InitializeUIElements();

        // Set up menu container visibility
        if (menuContainer != null)
        {
            menuContainer.style.display = DisplayStyle.Flex;
        }

        // Prepare fade overlay (full-screen VE above the menu)
        if (enableFadeIn && root != null && fadeOverlay == null)
        {
            fadeOverlay = new VisualElement();
            fadeOverlay.style.position = Position.Absolute;
            fadeOverlay.style.left = 0;
            fadeOverlay.style.top = 0;
            fadeOverlay.style.right = 0;
            fadeOverlay.style.bottom = 0;
            fadeOverlay.style.backgroundColor = new Color(0, 0, 0, 1);
            fadeOverlay.pickingMode = PickingMode.Ignore;
            root.Add(fadeOverlay);
        }
    }

    private void SetupStartMenuButtons()
    {
        // Clear existing menu buttons but preserve settings buttons
        var existingSettingsButtons = settingsButtons.ToList();
        menuButtons.Clear();
        settingsButtons = existingSettingsButtons;

        // Setup main menu buttons using our local button setup helper
        SetupButton("continue-button", ContinueGame);
        SetupButton("settings-button", OpenSettings);
        SetupButton("credits-button", ShowCredits);
        SetupButton("quit-game-button", QuitGame);

        // Ensure buttons are visible
        ShowStartMenu();
    }

    protected override void ShowStartMenu()
    {
        base.ShowStartMenu();
        
        // Restore the original title when returning from settings
        if (titleLabel != null)
        {
            titleLabel.text = originalTitle;
        }
    }

    protected override void ShowSettingsMenu()
    {
        base.ShowSettingsMenu();
        if (titleLabel != null)
        {
            titleLabel.text = "Settings";
        }
    }

    private void ValidateRequiredComponents()
    {
        // Don't require GameFlowManager when running as an overlay in the main scene
        if (GameFlowManager.Instance == null && !IsOverlaySetupPresent())
        {
            Debug.LogError("GameFlowManager singleton instance is not available in StartMenuManager");
        }
    }

    private void ContinueGame()
    {
        // If overlay setup exists, resume gameplay in-place
        if (IsOverlaySetupPresent())
        {
            ResumeGameplayFromOverlay();
            return;
        }

        // Fallback to scene-based flow
        if (GameFlowManager.Instance == null)
        {
            Debug.LogError("Cannot continue game: GameFlowManager is null");
            return;
        }
        GameFlowManager.Instance.LoadGame();
    }

    private void ShowCredits()
    {
        Debug.Log("Credits not yet implemented");
    }

    private void QuitGame()
    {
        if (GameFlowManager.Instance != null)
        {
            GameFlowManager.Instance.QuitGame();
        }
        else
        {
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
    }

    private bool IsOverlaySetupPresent()
    {
        return playerFpsCamera != null || playerCamera != null;
    }

    private void TryAutoAssignPlayerRefsIfMissing()
    {
        if (playerFpsCamera == null)
            playerFpsCamera = FindFirstObjectByType<FPSCharacterCamera>();
        if (playerCamera == null && playerFpsCamera != null)
            playerCamera = playerFpsCamera.GetComponent<Camera>();
        if (playerAudioListener == null && playerCamera != null)
            playerAudioListener = playerCamera.GetComponent<AudioListener>();
        if (playerController == null)
            playerController = FindFirstObjectByType<FPSCharacterController>();
        if (playerManager == null)
            playerManager = FindFirstObjectByType<FPSPlayerManager>();
        if (inventoryUI == null)
            inventoryUI = FindFirstObjectByType<InvUI>();
        if (pauseMenuManager == null)
            pauseMenuManager = FindFirstObjectByType<PauseMenuManager>();
        if (uiPanelManager == null)
            uiPanelManager = FindFirstObjectByType<UIPanelManager>();
    }

    private void ActivateOverlayMode()
    {
        overlayModeActive = true;

        // Prevent any saves for a brief window while overlay switches cameras/components
        if (PersistenceManager.Instance != null)
        {
            PersistenceManager.Instance.BeginShortNoSaveWindow();
            PersistenceManager.Instance.SetPlayerPositionSavingEnabled(false);
        }

        // Menu camera/audio ON (already handled in SelectAndActivateMenuCamera)
        // The active menu camera and its audio listener are already enabled

        // Player camera/audio OFF
        if (playerCamera != null) playerCamera.enabled = false;
        if (playerAudioListener != null) playerAudioListener.enabled = false;

        // Disable player input/movement and camera scripts to prevent cursor relock and rotation
        if (playerFpsCamera != null) playerFpsCamera.enabled = false;
        if (playerController != null) playerController.enabled = false;
        if (playerManager != null) playerManager.enabled = false;

        // Ensure gameplay UI (inventory/stash/shop) is closed so nothing shows behind the start menu
        CloseAllGameplayUI();

        // Disable pause menu ESC while start menu is open
        if (pauseMenuManager != null) pauseMenuManager.DisableEscKey();

        // Pause game if requested (rely on timeScale only to avoid restarting platforms on resume)
        if (pauseGameWhileMenuOpen)
        {
            Time.timeScale = 0f;
            StartCoroutine(EnforceMenuPause());
        }

        // Ensure cursor state and keep enforcing it while overlay is active
        UnityEngine.Cursor.visible = true;
        UnityEngine.Cursor.lockState = CursorLockMode.None;
        StartCoroutine(EnforceMenuCursor());
    }

    private void ResumeGameplayFromOverlay()
    {
        // Hide the UI document
        if (menuContainer != null)
        {
            menuContainer.style.display = DisplayStyle.None;
        }

        // Ensure gameplay UI is closed when resuming (handles trader/stash left open)
        CloseAllGameplayUI();

        // Menu camera/audio OFF
        if (activeMenuCamera != null)
        {
            activeMenuCamera.enabled = false;
            var activeListener = activeMenuCamera.GetComponent<AudioListener>();
            if (activeListener != null) activeListener.enabled = false;
        }

        // Player camera/audio ON
        if (playerCamera != null) playerCamera.enabled = true;
        if (playerAudioListener != null) playerAudioListener.enabled = true;

        // Re-enable player input/movement
        if (playerController != null) playerController.enabled = true;
        if (playerFpsCamera != null) playerFpsCamera.enabled = true;
        if (playerManager != null) playerManager.enabled = true;
        if (inventoryUI != null) inventoryUI.enabled = true;

        // Re-enable pause menu ESC handling
        if (pauseMenuManager != null) pauseMenuManager.EnableEscKey();

        // Resume game time
        if (pauseGameWhileMenuOpen)
        {
            Time.timeScale = 1f;
        }

        // Lock cursor for gameplay
        UnityEngine.Cursor.visible = false;
        UnityEngine.Cursor.lockState = CursorLockMode.Locked;

        overlayModeActive = false;

        // Disable UI document so it won't intercept clicks, but keep this component active for background tasks
        if (uiDocument != null)
        {
            uiDocument.enabled = false;
        }

        // Re-apply saved transform after overlay tear-down to counter any zero/reset from menu scene
        if (PersistenceManager.Instance != null)
        {
            PersistenceManager.Instance.ReapplySavedPlayerTransform();
            PersistenceManager.Instance.SetPlayerPositionSavingEnabled(true);
        }
    }

    public void ShowStartMenuOverlay()
    {
        // Ensure this overlay object and UI are enabled
        if (!gameObject.activeSelf)
        {
            gameObject.SetActive(true);
        }
        if (uiDocument != null && !uiDocument.enabled)
        {
            uiDocument.enabled = true;
        }

        // Rebind UI and buttons because UIDocument rebuilds the visual tree on enable
        InitializeUIElements();
        SetupStartMenuButtons();
        ShowStartMenu();

        TryAutoAssignPlayerRefsIfMissing();
        SelectAndActivateMenuCamera();
        ActivateOverlayMode();
        if (enableFadeIn)
        {
            StartCoroutine(FadeInFromBlack());
        }
    }

    private void SelectAndActivateMenuCamera()
    {
        Debug.Log($"[StartMenuManager] SelectAndActivateMenuCamera - Session selected: {_hasSelectedCameraThisSession}, Candidates: {candidateMenuCameras?.Count ?? 0}");

        Camera chosen = null;

        // Require at least one candidate camera
        if (candidateMenuCameras == null || candidateMenuCameras.Count == 0)
        {
            Debug.LogError("[StartMenuManager] No candidate cameras available! Please assign at least one camera to the candidateMenuCameras list.");
            return;
        }

        // On first session startup, prioritize stored preference, then compute nearest
        if (!_hasSelectedCameraThisSession)
        {
            Debug.Log("[StartMenuManager] First selection this session, checking stored preference first");
            string prefKey = PlayerPrefs.GetString(MENU_CAMERA_PREF_KEY, string.Empty);
            if (!string.IsNullOrEmpty(prefKey))
            {
                // Try to match by stable MenuCameraId first
                chosen = candidateMenuCameras.FirstOrDefault(c =>
                {
                    if (c == null) return false;
                    var id = c.GetComponent<MenuCameraId>();
                    return id != null && id.UniqueId == prefKey;
                });

                // Fallback to name match for legacy preference values
                if (chosen == null)
                {
                    chosen = candidateMenuCameras.FirstOrDefault(c => c != null && c.gameObject.name == prefKey);
                }

                if (chosen != null)
                {
                    Debug.Log($"[StartMenuManager] Using stored preference: {chosen.gameObject.name}");
                }
            }

            // If no stored preference or it didn't match, compute nearest
            if (chosen == null)
            {
                Debug.Log("[StartMenuManager] No valid stored preference, computing nearest camera");
                chosen = ComputeNearestMenuCamera();
            }
        }
        else
        {
            Debug.Log("[StartMenuManager] Subsequent selection, computing nearest camera");
            // For subsequent selections in the same session, always use nearest
            chosen = ComputeNearestMenuCamera();

            // If nearest failed, fall back to stored preference
            if (chosen == null)
            {
                Debug.Log("[StartMenuManager] Nearest camera computation failed, trying stored preference");
                string prefKey = PlayerPrefs.GetString(MENU_CAMERA_PREF_KEY, string.Empty);
                if (!string.IsNullOrEmpty(prefKey))
                {
                    chosen = candidateMenuCameras.FirstOrDefault(c =>
                    {
                        if (c == null) return false;
                        var id = c.GetComponent<MenuCameraId>();
                        return id != null && id.UniqueId == prefKey;
                    });

                    if (chosen == null)
                    {
                        chosen = candidateMenuCameras.FirstOrDefault(c => c != null && c.gameObject.name == prefKey);
                    }

                    if (chosen != null)
                    {
                        Debug.Log($"[StartMenuManager] Using stored preference fallback: {chosen.gameObject.name}");
                    }
                }
            }
        }

        // Final fallback: use first valid candidate camera
        if (chosen == null)
        {
            chosen = candidateMenuCameras.FirstOrDefault(c => c != null);
            if (chosen != null)
            {
                Debug.Log($"[StartMenuManager] Using first valid candidate camera: {chosen.gameObject.name}");
            }
        }

        // Save the chosen camera as preference for next time
        if (chosen != null)
        {
            SavePreferredCamera(chosen);
        }

        // Final validation
        if (chosen == null)
        {
            Debug.LogError("[StartMenuManager] Failed to select any menu camera!");
            return;
        }

        Debug.Log($"[StartMenuManager] Selected menu camera: {chosen.gameObject.name}");

        // Enable only the chosen menu camera; disable others
        foreach (var cam in candidateMenuCameras)
        {
            if (cam == null) continue;

            bool isChosen = (cam == chosen);
            cam.enabled = isChosen;

            // Manage audio listeners - only the chosen camera keeps its listener enabled
            var camListener = cam.GetComponent<AudioListener>();
            if (camListener != null)
            {
                camListener.enabled = isChosen;
            }
        }

        // Store reference to active camera
        activeMenuCamera = chosen;
        _hasSelectedCameraThisSession = true;
    }

    private Camera ComputeNearestMenuCamera()
    {
        Vector3 targetPos = Vector3.zero;
        bool haveLivePos = false;
        string positionSource = "none";

        // Prefer live, current player position when available
        if (playerController != null)
        {
            targetPos = playerController.transform.position;
            haveLivePos = true;
            positionSource = "playerController";
        }
        else if (playerFpsCamera != null)
        {
            targetPos = playerFpsCamera.transform.position;
            haveLivePos = true;
            positionSource = "playerFpsCamera";
        }
        // Fallback to last saved player position only if we don't have a live position
        if (!haveLivePos && PersistenceManager.Instance != null)
        {
            if (PersistenceManager.Instance.TryGetSavedPlayerTransform(out targetPos, out _))
            {
                positionSource = "savedPosition";
            }
        }

        Debug.Log($"[StartMenuManager] ComputeNearestMenuCamera - Target position: {targetPos} (source: {positionSource})");

        // If we don't have a valid position reference, we can't compute nearest
        if (targetPos == Vector3.zero && positionSource == "none")
        {
            Debug.Log("[StartMenuManager] No valid position reference available for nearest camera computation");
            return null;
        }

        float best = float.MaxValue;
        Camera nearest = null;
        foreach (var cam in candidateMenuCameras)
        {
            if (cam == null) continue;
            float d = (cam.transform.position - targetPos).sqrMagnitude;
            Debug.Log($"[StartMenuManager] Camera {cam.gameObject.name} distance: {Mathf.Sqrt(d):F2}");
            if (d < best)
            {
                best = d;
                nearest = cam;
            }
        }

        Debug.Log($"[StartMenuManager] Nearest camera: {nearest?.gameObject.name ?? "NULL"} (distance: {(nearest != null ? Mathf.Sqrt(best).ToString("F2") : "N/A")})");
        return nearest;
    }

    private void SavePreferredCamera(Camera cam)
    {
        if (cam == null) return;
        // Prefer stable unique id if present
        var id = cam.GetComponent<MenuCameraId>();
        string key = id != null ? id.UniqueId : cam.gameObject.name;
        PlayerPrefs.SetString(MENU_CAMERA_PREF_KEY, key);
        PlayerPrefs.Save();
    }

    // Allow other systems (pause menu) to preselect nearest camera before returning to title
    public void UpdatePreferredCameraToNearest()
    {
        if (candidateMenuCameras == null || candidateMenuCameras.Count == 0) return;
        var nearest = ComputeNearestMenuCamera();
        SavePreferredCamera(nearest);
    }

#if UNITY_EDITOR
    [ContextMenu("Clear Camera Preference (Testing)")]
    private void ClearCameraPreference()
    {
        PlayerPrefs.DeleteKey(MENU_CAMERA_PREF_KEY);
        PlayerPrefs.Save();
        _hasSelectedCameraThisSession = false;
        Debug.Log("[StartMenuManager] Cleared camera preference and reset session flag");
    }

    [ContextMenu("Force Reselect Camera")]
    private void ForceReselectCamera()
    {
        _hasSelectedCameraThisSession = false;
        SelectAndActivateMenuCamera();
    }

    [ContextMenu("Debug Camera Selection Info")]
    private void DebugCameraSelectionInfo()
    {
        Debug.Log($"=== Camera Selection Debug Info ===");
        Debug.Log($"Session selected: {_hasSelectedCameraThisSession}");
        Debug.Log($"Candidate cameras: {candidateMenuCameras?.Count ?? 0}");
        Debug.Log($"Active menu camera: {activeMenuCamera?.gameObject.name ?? "NULL"}");

        string prefKey = PlayerPrefs.GetString(MENU_CAMERA_PREF_KEY, string.Empty);
        Debug.Log($"Stored preference: '{prefKey}'");

        if (candidateMenuCameras != null)
        {
            for (int i = 0; i < candidateMenuCameras.Count; i++)
            {
                var cam = candidateMenuCameras[i];
                if (cam == null)
                {
                    Debug.Log($"  [{i}] NULL camera");
                    continue;
                }

                var id = cam.GetComponent<MenuCameraId>();
                string idStr = id != null ? id.UniqueId : "NO_ID";
                Debug.Log($"  [{i}] {cam.gameObject.name} (ID: {idStr}, Enabled: {cam.enabled})");
            }
        }

        // Test position sources
        Vector3 playerPos = Vector3.zero;
        string posSource = "none";
        if (playerController != null)
        {
            playerPos = playerController.transform.position;
            posSource = "playerController";
        }
        else if (playerFpsCamera != null)
        {
            playerPos = playerFpsCamera.transform.position;
            posSource = "playerFpsCamera";
        }
        else if (PersistenceManager.Instance != null && PersistenceManager.Instance.TryGetSavedPlayerTransform(out Vector3 savedPos, out _))
        {
            playerPos = savedPos;
            posSource = "savedPosition";
        }

        Debug.Log($"Player position: {playerPos} (source: {posSource})");
        Debug.Log($"=== End Debug Info ===");
    }
#endif

    private System.Collections.IEnumerator BackgroundPreferredCameraUpdater()
    {
        var wait = new WaitForSecondsRealtime(Mathf.Max(5f, cameraPreferenceUpdateIntervalSeconds));
        while (true)
        {
            yield return wait;
            if (candidateMenuCameras != null && candidateMenuCameras.Count > 0)
            {
                var nearest = ComputeNearestMenuCamera();
                SavePreferredCamera(nearest);
            }
        }
    }

    private System.Collections.IEnumerator FadeInFromBlack()
    {
        if (fadeOverlay == null)
            yield break;
        fadeOverlay.style.display = DisplayStyle.Flex;
        float t = 0f;
        // Make sure starting fully opaque
        fadeOverlay.style.backgroundColor = new Color(0, 0, 0, 1f);
        while (t < fadeInDuration)
        {
            t += Time.unscaledDeltaTime;
            float a = Mathf.Lerp(1f, 0f, Mathf.Clamp01(t / fadeInDuration));
            fadeOverlay.style.backgroundColor = new Color(0, 0, 0, a);
            yield return null;
        }
        // Hide overlay after fade
        fadeOverlay.style.backgroundColor = new Color(0, 0, 0, 0f);
        fadeOverlay.style.display = DisplayStyle.None;
    }

    private System.Collections.IEnumerator EnforceMenuPause()
    {
        // Keep time frozen while overlay is active to avoid other systems resuming time
        while (overlayModeActive && pauseGameWhileMenuOpen)
        {
            if (Time.timeScale != 0f)
            {
                Time.timeScale = 0f;
            }
            yield return null;
        }
    }

    private System.Collections.IEnumerator EnforceMenuCursor()
    {
        // Prevent other systems from re-locking the cursor while the start menu is open
        while (overlayModeActive)
        {
            if (UnityEngine.Cursor.lockState != CursorLockMode.None || !UnityEngine.Cursor.visible)
            {
                UnityEngine.Cursor.lockState = CursorLockMode.None;
                UnityEngine.Cursor.visible = true;
            }
            yield return null;
        }
    }

    private void CloseAllGameplayUI()
    {
        // Close shop/stash via UIPanelManager if available
        if (uiPanelManager == null)
            uiPanelManager = FindFirstObjectByType<UIPanelManager>();
        if (uiPanelManager != null)
        {
            uiPanelManager.CloseShop();
            uiPanelManager.CloseStash();
        }

        // Close inventory if open
        if (inventoryUI == null)
            inventoryUI = FindFirstObjectByType<InvUI>();
        if (inventoryUI != null && inventoryUI.IsInventoryVisible())
        {
            inventoryUI.CloseInventory();
        }
    }
}