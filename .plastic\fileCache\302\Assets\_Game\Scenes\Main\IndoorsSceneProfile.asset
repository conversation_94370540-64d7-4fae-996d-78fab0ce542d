%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9187351027170256919
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a81bcacc415a1f743bfdf703afc52027, type: 3}
  m_Name: GradientSky
  m_EditorClassIdentifier: 
  active: 1
  rotation:
    m_OverrideState: 0
    m_Value: 0
  skyIntensityMode:
    m_OverrideState: 0
    m_Value: 0
  exposure:
    m_OverrideState: 0
    m_Value: 1.69
  multiplier:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxValue:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxColor:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  desiredLuxValue:
    m_OverrideState: 0
    m_Value: 20000
  updateMode:
    m_OverrideState: 0
    m_Value: 0
  updatePeriod:
    m_OverrideState: 0
    m_Value: 0
  includeSunInBaking:
    m_OverrideState: 0
    m_Value: 0
  top:
    m_OverrideState: 1
    m_Value: {r: 0.06341225, g: 0.080572695, b: 0.1792453, a: 1}
  middle:
    m_OverrideState: 1
    m_Value: {r: 0.047525797, g: 0.047525797, b: 0.11320752, a: 1}
  bottom:
    m_OverrideState: 1
    m_Value: {r: 0.031639382, g: 0.031639382, b: 0.084905684, a: 1}
  gradientDiffusion:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-6747788842634609719
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f086a068d4c5889438831b3ae9afc11c, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 1
  useFullACES:
    m_OverrideState: 1
    m_Value: 1
  toeStrength:
    m_OverrideState: 0
    m_Value: 0
  toeLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderStrength:
    m_OverrideState: 0
    m_Value: 0
  shoulderLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderAngle:
    m_OverrideState: 0
    m_Value: 0
  gamma:
    m_OverrideState: 0
    m_Value: 1
  lutTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  lutContribution:
    m_OverrideState: 0
    m_Value: 1
  neutralHDRRangeReductionMode:
    m_OverrideState: 0
    m_Value: 2
  acesPreset:
    m_OverrideState: 0
    m_Value: 3
  fallbackMode:
    m_OverrideState: 0
    m_Value: 1
  hueShiftAmount:
    m_OverrideState: 0
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 0
    m_Value: 0
  paperWhite:
    m_OverrideState: 0
    m_Value: 300
  detectBrightnessLimits:
    m_OverrideState: 0
    m_Value: 1
  minNits:
    m_OverrideState: 0
    m_Value: 0.005
  maxNits:
    m_OverrideState: 0
    m_Value: 1000
--- !u!114 &-5535885722284554385
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4b8bcdf71d7fafa419fca1ed162f5fc9, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  postExposure:
    m_OverrideState: 0
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 10
  colorFilter:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  hueShift:
    m_OverrideState: 0
    m_Value: 0
  saturation:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-5509824568003218647
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7ddcec8a8eb2d684d833ac8f5d26aebd, type: 3}
  m_Name: HDShadowSettings
  m_EditorClassIdentifier: 
  active: 1
  interCascadeBorders: 1
  maxShadowDistance:
    m_OverrideState: 1
    m_Value: 7601
  directionalTransmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  cascadeShadowSplitCount:
    m_OverrideState: 0
    m_Value: 4
  cascadeShadowSplit0:
    m_OverrideState: 0
    m_Value: 0.05
  cascadeShadowSplit1:
    m_OverrideState: 0
    m_Value: 0.15
  cascadeShadowSplit2:
    m_OverrideState: 0
    m_Value: 0.3
  cascadeShadowBorder0:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder1:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder2:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder3:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-3589395413827545121
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 3
  threshold:
    m_OverrideState: 1
    m_Value: 1
  intensity:
    m_OverrideState: 1
    m_Value: 1
  scatter:
    m_OverrideState: 1
    m_Value: 0.7
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
  anamorphic:
    m_OverrideState: 0
    m_Value: 1
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 1
    m_Value: 0
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-1683328151786585248
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da5ab44aadfb1804db5fd470983ac1b8, type: 3}
  m_Name: LiftGammaGain
  m_EditorClassIdentifier: 
  active: 1
  lift:
    m_OverrideState: 0
    m_Value: {x: 0.93, y: 1, z: 0.94983333, w: 0}
  gamma:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: -0.20415178}
  gain:
    m_OverrideState: 0
    m_Value: {x: 0.5626667, y: 1, z: 0.36, w: 0}
--- !u!114 &-1197279436182527482
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f034cba68ab55e046ae1445a42f18c0e, type: 3}
  m_Name: IndirectLightingController
  m_EditorClassIdentifier: 
  active: 0
  indirectDiffuseLightingMultiplier:
    m_OverrideState: 0
    m_Value: 3.44
  indirectDiffuseLightingLayers:
    m_OverrideState: 0
    m_Value: 65535
  reflectionLightingMultiplier:
    m_OverrideState: 0
    m_Value: 1
  reflectionLightingLayers:
    m_OverrideState: 0
    m_Value: 65535
  reflectionProbeIntensityMultiplier:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &-1014077452862362273
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15cc4c5fcb677014ebdc0d8be227b40c, type: 3}
  m_Name: ScreenSpaceLensFlare
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 1
  tintColor:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  bloomMip:
    m_OverrideState: 1
    m_Value: 1
  firstFlareIntensity:
    m_OverrideState: 0
    m_Value: 1
  secondaryFlareIntensity:
    m_OverrideState: 0
    m_Value: 1
  warpedFlareIntensity:
    m_OverrideState: 0
    m_Value: 1
  warpedFlareScale:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1}
  samples:
    m_OverrideState: 0
    m_Value: 1
  sampleDimmer:
    m_OverrideState: 0
    m_Value: 0.5
  vignetteEffect:
    m_OverrideState: 0
    m_Value: 1
  startingPosition:
    m_OverrideState: 0
    m_Value: 1.5
  scale:
    m_OverrideState: 0
    m_Value: 1
  streaksIntensity:
    m_OverrideState: 1
    m_Value: 0
  streaksLength:
    m_OverrideState: 0
    m_Value: 0.5
  streaksOrientation:
    m_OverrideState: 0
    m_Value: 0
  streaksThreshold:
    m_OverrideState: 0
    m_Value: 0.25
  resolution:
    m_OverrideState: 0
    m_Value: 4
  spectralLut:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  chromaticAbberationIntensity:
    m_OverrideState: 0
    m_Value: 0.5
  chromaticAbberationSampleCount:
    m_OverrideState: 0
    m_Value: 3
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: IndoorsSceneProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 4676980873293090088}
  - {fileID: 7632258061915546295}
  - {fileID: 9032355459120251522}
  - {fileID: -3589395413827545121}
  - {fileID: -6747788842634609719}
  - {fileID: -1683328151786585248}
  - {fileID: -1014077452862362273}
  - {fileID: 773934254324874839}
  - {fileID: -5535885722284554385}
  - {fileID: -5509824568003218647}
  - {fileID: 3774579973056845410}
  - {fileID: -9187351027170256919}
  - {fileID: -1197279436182527482}
  - {fileID: 5193391513821867827}
--- !u!114 &773934254324874839
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c1be1b6c95cd2e41b27903b9270817f, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 0
    m_Value: 0
  color:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  center:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.5
  smoothness:
    m_OverrideState: 0
    m_Value: 0.2
  roundness:
    m_OverrideState: 1
    m_Value: 0.5
  rounded:
    m_OverrideState: 0
    m_Value: 0
  mask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  opacity:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &3774579973056845410
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a76fd08475e21554b8f284f723dd7cf8, type: 3}
  m_Name: WaterRendering
  m_EditorClassIdentifier: 
  active: 1
  enable:
    m_OverrideState: 1
    m_Value: 1
  triangleSize:
    m_OverrideState: 0
    m_Value: 100
  ambientProbeDimmer:
    m_OverrideState: 0
    m_Value: 0.5
--- !u!114 &4676980873293090088
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d7593b3a9277ac4696b20006c21dde2, type: 3}
  m_Name: VisualEnvironment
  m_EditorClassIdentifier: 
  active: 1
  skyType:
    m_OverrideState: 1
    m_Value: 3
  cloudType:
    m_OverrideState: 0
    m_Value: 0
  skyAmbientMode:
    m_OverrideState: 0
    m_Value: 1
  planetRadius:
    m_OverrideState: 0
    m_Value: 6378.1
  renderingSpace:
    m_OverrideState: 0
    m_Value: 1
  centerMode:
    m_OverrideState: 0
    m_Value: 0
  planetCenter:
    m_OverrideState: 0
    m_Value: {x: 0, y: -6378.1, z: 0}
  windOrientation:
    m_OverrideState: 0
    m_Value: 0
  windSpeed:
    m_OverrideState: 0
    m_Value: 0
  fogType:
    m_OverrideState: 0
    m_Value: 0
  m_Version: 1
--- !u!114 &5193391513821867827
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: de968e67585ccdc4f8b6e2d379676a66, type: 3}
  m_Name: JPEGCompression
  m_EditorClassIdentifier: 
  active: 0
  JPEGComputeShaderParameter:
    m_OverrideState: 1
    m_Value: {fileID: 7200000, guid: 795d453efbfb0e24bb4e990cd6c4aa37, type: 3}
  useSpatialCompression:
    m_OverrideState: 1
    m_Value: 1
  screenDownsampling:
    m_OverrideState: 1
    m_Value: 1
  usePointFiltering:
    m_OverrideState: 1
    m_Value: 1
  compressionThreshold:
    m_OverrideState: 1
    m_Value: 0
  fastPerformanceMode:
    m_OverrideState: 0
    m_Value: 0
  useTemporalCompression:
    m_OverrideState: 1
    m_Value: 1
  useIFrames:
    m_OverrideState: 1
    m_Value: 1
  numBFrames:
    m_OverrideState: 1
    m_Value: 0
  bitrate:
    m_OverrideState: 1
    m_Value: 0
  bitrateArtifacts:
    m_OverrideState: 1
    m_Value: 0
  motionRenderOutTexture: {fileID: 0}
--- !u!114 &7632258061915546295
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  meteringMode:
    m_OverrideState: 1
    m_Value: 2
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 1
    m_Value: 1
  compensation:
    m_OverrideState: 0
    m_Value: 0
  limitMin:
    m_OverrideState: 0
    m_Value: -1
  limitMax:
    m_OverrideState: 0
    m_Value: 14
  curveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 20
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMinCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -12
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 18
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMaxCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -8
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 22
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 0
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 0
    m_Value: 3
  adaptationSpeedLightToDark:
    m_OverrideState: 0
    m_Value: 1
  weightTextureMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  histogramPercentages:
    m_OverrideState: 0
    m_Value: {x: 40, y: 90}
  histogramUseCurveRemapping:
    m_OverrideState: 0
    m_Value: 0
  targetMidGray:
    m_OverrideState: 0
    m_Value: 0
  centerAroundExposureTarget:
    m_OverrideState: 0
    m_Value: 0
  proceduralCenter:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  proceduralRadii:
    m_OverrideState: 0
    m_Value: {x: 0.3, y: 0.3}
  maskMinIntensity:
    m_OverrideState: 0
    m_Value: -30
  maskMaxIntensity:
    m_OverrideState: 0
    m_Value: 30
  proceduralSoftness:
    m_OverrideState: 0
    m_Value: 0.5
--- !u!114 &9032355459120251522
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 953beb541740ddc499d005ee80c9ff29, type: 3}
  m_Name: Fog
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 0
    m_Value: 3
  enabled:
    m_OverrideState: 1
    m_Value: 1
  colorMode:
    m_OverrideState: 0
    m_Value: 0
  color:
    m_OverrideState: 0
    m_Value: {r: 0.023228915, g: 0.033005293, b: 0.05660379, a: 1}
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  maxFogDistance:
    m_OverrideState: 1
    m_Value: 5000
  mipFogMaxMip:
    m_OverrideState: 0
    m_Value: 0.5
  mipFogNear:
    m_OverrideState: 0
    m_Value: 0
  mipFogFar:
    m_OverrideState: 0
    m_Value: 1000
  baseHeight:
    m_OverrideState: 1
    m_Value: 600
  maximumHeight:
    m_OverrideState: 1
    m_Value: 7700
  meanFreePath:
    m_OverrideState: 1
    m_Value: 350
  enableVolumetricFog:
    m_OverrideState: 1
    m_Value: 1
  albedo:
    m_OverrideState: 1
    m_Value: {r: 0.2971698, g: 0.37447318, b: 1, a: 1}
  globalLightProbeDimmer:
    m_OverrideState: 1
    m_Value: 1
  depthExtent:
    m_OverrideState: 1
    m_Value: 5000
  denoisingMode:
    m_OverrideState: 1
    m_Value: 3
  anisotropy:
    m_OverrideState: 0
    m_Value: 0
  sliceDistributionUniformity:
    m_OverrideState: 0
    m_Value: 0.75
  multipleScatteringIntensity:
    m_OverrideState: 0
    m_Value: 2
  m_FogControlMode:
    m_OverrideState: 1
    m_Value: 0
  screenResolutionPercentage:
    m_OverrideState: 1
    m_Value: 24
  volumeSliceCount:
    m_OverrideState: 1
    m_Value: 104
  m_VolumetricFogBudget:
    m_OverrideState: 1
    m_Value: 0.3
  m_ResolutionDepthRatio:
    m_OverrideState: 1
    m_Value: 0.5
  directionalLightsOnly:
    m_OverrideState: 0
    m_Value: 1
