[Licensing::Module] Trying to connect to existing licensing client channel...
Built from '6000.2/respin/6000.2.0f1-517f89d850d1' branch; Version is '6000.2.0f1 (eed1c594c913) revision 15651269'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias" at "2025-08-17T18:07:36.848107Z"
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-17T18:07:36Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.2.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker0.log
-srvPort
50817
-licensingIpc
LicenseClient-ilias
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [36764]  Target information:

Player connection [36764]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2094663619 [EditorId] 2094663619 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36764]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2094663619 [EditorId] 2094663619 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36764]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2094663619 [EditorId] 2094663619 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36764]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2094663619 [EditorId] 2094663619 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36764]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2094663619 [EditorId] 2094663619 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36764] Host joined multi-casting on [***********:54997]...
Player connection [36764] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 33088, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.1'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-ilias"
[Licensing::IpcConnector] LicenseClient-ilias channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0" at "2025-08-17T18:07:36.9344605Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 34452, path: "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              e1ef48111fb141a09f8d42e0b4fcec21
  Correlation Id:          07b42c3946518dbd4b3a31c74382467b
  External correlation Id: 5740597925930531936
  Machine Id:              /GaE4A2aXaDfTh/RbExLPR8H29M=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-ilias-6000.2.0" (connect: 0.00s, validation: 0.00s, handshake: 0.01s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0-notifications" at "2025-08-17T18:07:36.9502211Z"
[Licensing::Module] Licensing Background thread has ended after 0.10s
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 243.88 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.8088
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56744
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006582 seconds.
- Loaded All Assemblies, in  0.584 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.417 seconds
Domain Reload Profiling: 987ms
	BeginReloadAssembly (203ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (92ms)
	LoadAllAssembliesAndSetupDomain (201ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (195ms)
				TypeCache.ScanAssembly (179ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (417ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (360ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (65ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (136ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.470 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 19.34 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.450 seconds
Domain Reload Profiling: 2894ms
	BeginReloadAssembly (274ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (1044ms)
		LoadAssemblies (794ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (407ms)
			TypeCache.Refresh (317ms)
				TypeCache.ScanAssembly (294ms)
			BuildScriptInfoCaches (75ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1451ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (248ms)
			ProcessInitializeOnLoadAttributes (554ms)
			ProcessInitializeOnLoadMethodAttributes (437ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 22.57 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.1 MB). Loaded Objects now: 8980.
Memory consumption went from 225.4 MB to 219.3 MB.
Total: 16.646400 ms (FindLiveObjects: 1.157300 ms CreateObjectMapping: 1.255500 ms MarkObjects: 9.761500 ms  DeleteObjects: 4.470700 ms)

========================================================================
Received Import Request.
  Time since last request: 407.867255 seconds.
  path: Assets/_Game/Resources/ItemsIcons/thumbnail.png
  artifactKey: Guid(2285ae3b2b7444340b2ace637e2367ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/thumbnail.png using Guid(2285ae3b2b7444340b2ace637e2367ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dfe36953d04b12cff0a888e0c034cfc2') in 0.1313794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/_Game/Resources/ItemsIcons/thumbnail (3).png
  artifactKey: Guid(b5375442d6105e040853f7a1a7dcc328) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/thumbnail (3).png using Guid(b5375442d6105e040853f7a1a7dcc328) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd1079c4b40ce7773358ceedf92c99844') in 0.0207355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_Game/Resources/Smoke.png
  artifactKey: Guid(341e07a3240b22745baad2c2452de775) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Smoke.png using Guid(341e07a3240b22745baad2c2452de775) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd29ecf5b25fd982dfd0d7b7586f40afa') in 0.0261859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_Game/Resources/ItemsIcons/thumbnail (4).png
  artifactKey: Guid(d9c15e888565d3b4aafc99a3e65f12f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/thumbnail (4).png using Guid(d9c15e888565d3b4aafc99a3e65f12f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2271a8710b8d3f39a110baee5b503229') in 0.0223061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.194 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.11 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.257 seconds
Domain Reload Profiling: 5426ms
	BeginReloadAssembly (693ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (184ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (3211ms)
		LoadAssemblies (2995ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (700ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (665ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1257ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (917ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (252ms)
			ProcessInitializeOnLoadAttributes (504ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 21.00 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.0 MB). Loaded Objects now: 9013.
Memory consumption went from 239.3 MB to 233.3 MB.
Total: 10.233200 ms (FindLiveObjects: 1.062700 ms CreateObjectMapping: 0.818500 ms MarkObjects: 5.151500 ms  DeleteObjects: 3.199300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.539 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 18.38 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.320 seconds
Domain Reload Profiling: 2832ms
	BeginReloadAssembly (412ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (135ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (998ms)
		LoadAssemblies (831ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (326ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (290ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1321ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1089ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (256ms)
			ProcessInitializeOnLoadAttributes (661ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 21.60 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.8 MB). Loaded Objects now: 9015.
Memory consumption went from 239.3 MB to 233.5 MB.
Total: 11.475300 ms (FindLiveObjects: 1.062000 ms CreateObjectMapping: 0.889000 ms MarkObjects: 6.129900 ms  DeleteObjects: 3.392900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.71 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7926 unused Assets / (3.1 MB). Loaded Objects now: 9016.
Memory consumption went from 239.3 MB to 236.2 MB.
Total: 94.200300 ms (FindLiveObjects: 1.650000 ms CreateObjectMapping: 1.643100 ms MarkObjects: 87.352500 ms  DeleteObjects: 3.552700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.474 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.30 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.135 seconds
Domain Reload Profiling: 2582ms
	BeginReloadAssembly (390ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (919ms)
		LoadAssemblies (796ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (266ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1136ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (861ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (229ms)
			ProcessInitializeOnLoadAttributes (481ms)
			ProcessInitializeOnLoadMethodAttributes (121ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 22.05 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7975 unused Assets / (5.9 MB). Loaded Objects now: 9018.
Memory consumption went from 239.4 MB to 233.5 MB.
Total: 11.069200 ms (FindLiveObjects: 1.086800 ms CreateObjectMapping: 0.768300 ms MarkObjects: 5.816800 ms  DeleteObjects: 3.395600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1674.870278 seconds.
  path: Assets/_Game/Scripts/Core/CurrencyManager.cs
  artifactKey: Guid(f68b3bedf018da047b5d42ea18842176) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Core/CurrencyManager.cs using Guid(f68b3bedf018da047b5d42ea18842176) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b62632bbf370908a57e0483750284c94') in 0.0607554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 19.34 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7926 unused Assets / (5.7 MB). Loaded Objects now: 9018.
Memory consumption went from 239.4 MB to 233.7 MB.
Total: 13.086900 ms (FindLiveObjects: 1.165300 ms CreateObjectMapping: 1.453200 ms MarkObjects: 7.226500 ms  DeleteObjects: 3.240800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4.911890 seconds.
  path: Assets/_Game/Scripts/Core/CurrencyManager.cs
  artifactKey: Guid(f68b3bedf018da047b5d42ea18842176) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Core/CurrencyManager.cs using Guid(f68b3bedf018da047b5d42ea18842176) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd1e9f4730dda12009ddebc3302e598b6') in 0.009516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 6.390799 seconds.
  path: Assets/_Game/Scripts/Core/PersistenceDebugger.cs
  artifactKey: Guid(f1cefc4b9764f924090f3f22015c7a97) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Core/PersistenceDebugger.cs using Guid(f1cefc4b9764f924090f3f22015c7a97) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a82908c2b7da57ad34fa31dc0c89759') in 0.0009711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.33 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7925 unused Assets / (5.9 MB). Loaded Objects now: 9017.
Memory consumption went from 239.4 MB to 233.5 MB.
Total: 15.054800 ms (FindLiveObjects: 1.213500 ms CreateObjectMapping: 1.527900 ms MarkObjects: 8.188900 ms  DeleteObjects: 4.123500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 8.667340 seconds.
  path: Assets/_Recovery/0.unity
  artifactKey: Guid(45cfe1a3169725e40aed9d7545be45bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Recovery/0.unity using Guid(45cfe1a3169725e40aed9d7545be45bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '49305a7bb5c0c865148a6485cd2a2ade') in 0.0127303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.788362 seconds.
  path: Assets/_Game/Scripts/Core/fix.txt
  artifactKey: Guid(ea05a4b031b718e4a85151116d35406a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Core/fix.txt using Guid(ea05a4b031b718e4a85151116d35406a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58a236ce01d3999a1b45b8d55699c677') in 0.0203755 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.639 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.11 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.257 seconds
Domain Reload Profiling: 2872ms
	BeginReloadAssembly (370ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (1129ms)
		LoadAssemblies (971ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (330ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (292ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1257ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1010ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (274ms)
			ProcessInitializeOnLoadAttributes (573ms)
			ProcessInitializeOnLoadMethodAttributes (128ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 19.29 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.8 MB). Loaded Objects now: 9019.
Memory consumption went from 239.5 MB to 233.7 MB.
Total: 10.862700 ms (FindLiveObjects: 0.744000 ms CreateObjectMapping: 0.903700 ms MarkObjects: 6.117700 ms  DeleteObjects: 3.096000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 10.910066 seconds.
  path: Assets/_Recovery
  artifactKey: Guid(aecb82cf76f21c0498da9bb11203d117) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Recovery using Guid(aecb82cf76f21c0498da9bb11203d117) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0026cdbd35e2d2ae05488571c0bbdc0f') in 0.0085595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 103.828049 seconds.
  path: ProjectSettings/EditorBuildSettings.asset
  artifactKey: Guid(0000000000000000b000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing ProjectSettings/EditorBuildSettings.asset using Guid(0000000000000000b000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '857d84d8067ef22a18fe0c43f8854f29') in 0.017371 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.910259 seconds.
  path: Assets/Settings/Build Profiles/New Windows Profile.asset
  artifactKey: Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Build Profiles/New Windows Profile.asset using Guid(62661ce3f939807418bc7e6735d742b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '07943915b7962d622c6de150b3991a01') in 0.0335997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 18.861628 seconds.
  path: Assets/_Game/Materials/cashincashout.png
  artifactKey: Guid(58bdc1db6b122854db650d79eed64a80) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Materials/cashincashout.png using Guid(58bdc1db6b122854db650d79eed64a80) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e3e6f8713d2895a105dd01f14fd795ad') in 0.0679379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 40.581432 seconds.
  path: Assets/_Game/Resources/ItemsIcons/Graplin.png
  artifactKey: Guid(06767cbccef927c4ebe4a220ac4b5c90) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/Graplin.png using Guid(06767cbccef927c4ebe4a220ac4b5c90) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a75882e2a254cf01906d9b66a4827530') in 0.0220545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 26.758934 seconds.
  path: Assets/_Game/Scripts/Interface/Crosshair
  artifactKey: Guid(c7452ef9d63db48458f07c832017883b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Crosshair using Guid(c7452ef9d63db48458f07c832017883b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '539f81a3ce2ca6b56fafdc188c2ab7ee') in 0.0007569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.760005 seconds.
  path: Assets/_Game/Scripts/Interface/Crosshair/Marker.png
  artifactKey: Guid(dc3d326c24d107b4898f331c08e8cbd9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Crosshair/Marker.png using Guid(dc3d326c24d107b4898f331c08e8cbd9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7e5b16e3e332618977edf8ab323821fa') in 0.0170038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 36.166694 seconds.
  path: Assets/_Game/Resources/UI/Placeholder_Bag.png
  artifactKey: Guid(74743076af2b40a4c8f093981a264bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/Placeholder_Bag.png using Guid(74743076af2b40a4c8f093981a264bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e8d2980f57d99261a113b4ba2356dc4d') in 0.019696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 4.558847 seconds.
  path: Assets/Graphy - Ultimate Stats Monitor/Textures/Rounded_Rect_10px.png
  artifactKey: Guid(c4f7f8debbcf3cf4faf280628cab55f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Graphy - Ultimate Stats Monitor/Textures/Rounded_Rect_10px.png using Guid(c4f7f8debbcf3cf4faf280628cab55f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15110b53f7620c3d39a822537146d281') in 0.0178911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.572573 seconds.
  path: Assets/_Game/Resources/smoke2.png
  artifactKey: Guid(fcec68f1475799b46877565e8bef5a3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/smoke2.png using Guid(fcec68f1475799b46877565e8bef5a3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b122ef47b54f24434afb4db3a35ccc8') in 0.0149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 9.526942 seconds.
  path: Assets/_Game/Resources/ItemsIcons/Rope.png
  artifactKey: Guid(1df668a5a178b484cb369bfe829760c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/Rope.png using Guid(1df668a5a178b484cb369bfe829760c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95385fe98dc41a89b6bb47fa307b60a4') in 0.0184427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.129901 seconds.
  path: Assets/_Game/Resources/UI/Placeholder_Helmet.png
  artifactKey: Guid(dec077adde63d60419133ee65217fc44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/Placeholder_Helmet.png using Guid(dec077adde63d60419133ee65217fc44) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9241d6d8ef4c6ef3bf0d8375794ac9a3') in 0.0177578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 1.894397 seconds.
  path: Assets/Graphy - Ultimate Stats Monitor/Textures/2x2_Texture.png
  artifactKey: Guid(ad4148593b05d0f47980774815c325fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Graphy - Ultimate Stats Monitor/Textures/2x2_Texture.png using Guid(ad4148593b05d0f47980774815c325fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '57525d6f6656c9972b84018a664e794e') in 0.0180853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_Game/Resources/UI/BatteryIcon_0.png
  artifactKey: Guid(c3643d31f1a42ae4191b546e9effbcd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/BatteryIcon_0.png using Guid(c3643d31f1a42ae4191b546e9effbcd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ec9e4cfca8d11f8554bd9177fd366a69') in 0.0214276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_Game/Resources/smoke4.png
  artifactKey: Guid(07b2202272c6f0f46ab01b0ede34e45e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/smoke4.png using Guid(07b2202272c6f0f46ab01b0ede34e45e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f5df9c308dc63148268f200f9b78f67') in 0.0217334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_Game/Resources/ItemsIcons/Rope-thumbnail.png
  artifactKey: Guid(b71f72c143e5204409f7a15d31aa3685) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/Rope-thumbnail.png using Guid(b71f72c143e5204409f7a15d31aa3685) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ee9eaf76421dad1f798cbf63593f40a') in 0.0200335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Resources/ItemsIcons/silhouette 2.png
  artifactKey: Guid(a271626c6396ae140bbb978c54b0516d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/silhouette 2.png using Guid(a271626c6396ae140bbb978c54b0516d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e7c21a29420e822791d94f5b72d0c90') in 0.0392752 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Graphy - Ultimate Stats Monitor/Textures/Manager_Logo_Dark.png
  artifactKey: Guid(2967191e0207a36479ba2e37accf4403) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Graphy - Ultimate Stats Monitor/Textures/Manager_Logo_Dark.png using Guid(2967191e0207a36479ba2e37accf4403) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ad1d302e8cc48bfb4cc57a9bdddc91d3') in 0.0181491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_Game/Resources/UI/BatteryIcon_50.png
  artifactKey: Guid(30e52eaf905df804a8a2c64d0fa888d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/BatteryIcon_50.png using Guid(30e52eaf905df804a8a2c64d0fa888d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14c444b29ba553ceb901c1654a5f3eb2') in 0.0182937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_Game/Resources/ItemsIcons/silhouette.png
  artifactKey: Guid(48007d4818c15814dac329c357250eef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/silhouette.png using Guid(48007d4818c15814dac329c357250eef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'daeed84c3cd8f0b82ba977d9d22dd73f') in 0.0285144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Resources/ItemsIcons/gun.png
  artifactKey: Guid(fa516a5ad8c535d46b8ba8f8048b2e7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/gun.png using Guid(fa516a5ad8c535d46b8ba8f8048b2e7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '631e415c4f714b57d7c7cbbf4edcb2f4') in 0.0220576 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Graphy - Ultimate Stats Monitor/Textures/Manager_Logo_White.png
  artifactKey: Guid(468dad608405ce74f99362912f165deb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Graphy - Ultimate Stats Monitor/Textures/Manager_Logo_White.png using Guid(468dad608405ce74f99362912f165deb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c159bd49c5f27ace43ec948700d6ca9') in 0.0257147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_Game/Resources/smoke3.png
  artifactKey: Guid(25d90087fa5730d45acf7c384797c448) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/smoke3.png using Guid(25d90087fa5730d45acf7c384797c448) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '218e0ceb8365f9dc7add7e11155fb5aa') in 0.0205193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_Game/Resources/UI/energy_bar_fill.png
  artifactKey: Guid(125c198469ba96a4394ded4445991954) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/energy_bar_fill.png using Guid(125c198469ba96a4394ded4445991954) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc0b89b42a8bc4fcc46811d4f9e82fdf') in 0.0195155 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Resources/UI/bolt.png
  artifactKey: Guid(3159210fd4a256743962fad87601a17c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/bolt.png using Guid(3159210fd4a256743962fad87601a17c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcb4259c9b42f66cb431ccfab4e72e7e') in 0.0290209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Graphy - Ultimate Stats Monitor/Textures/Debugger_Logo_White.png
  artifactKey: Guid(db20accdfca9af54c8673b4083d331b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Graphy - Ultimate Stats Monitor/Textures/Debugger_Logo_White.png using Guid(db20accdfca9af54c8673b4083d331b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50280ebb21c76a24ff1714456c309ba0') in 0.0228722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_Game/Resources/UI/energy_bar_bg.png
  artifactKey: Guid(a43431fe47bad7946bb0e45273640ef7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/energy_bar_bg.png using Guid(a43431fe47bad7946bb0e45273640ef7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ef3fc8df8d1960cd0086f48173c10846') in 0.0200729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0