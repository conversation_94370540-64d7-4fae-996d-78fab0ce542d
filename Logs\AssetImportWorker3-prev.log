[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias" at "2025-08-17T22:51:54.1186545Z"
Built from '6000.2/respin/6000.2.0f1-517f89d850d1' branch; Version is '6000.2.0f1 (eed1c594c913) revision 15651269'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-17T22:51:54Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.2.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker3.log
-srvPort
50817
-licensingIpc
LicenseClient-ilias
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [66076]  Target information:

Player connection [66076]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 167758061 [EditorId] 167758061 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [66076]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 167758061 [EditorId] 167758061 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [66076]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 167758061 [EditorId] 167758061 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [66076]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 167758061 [EditorId] 167758061 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [66076]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 167758061 [EditorId] 167758061 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [66076] Host joined multi-casting on [***********:54997]...
Player connection [66076] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 33088, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.1'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-ilias"
[Licensing::IpcConnector] LicenseClient-ilias channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0" at "2025-08-17T22:51:54.3243741Z"
Library Redirect Path: Library/
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 34452, path: "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              7b447216d1ad4747ae8a9bcd9284b7d8
  Correlation Id:          07b42c3946518dbd4b3a31c74382467b
  External correlation Id: 5137525794566889205
  Machine Id:              /GaE4A2aXaDfTh/RbExLPR8H29M=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-ilias-6000.2.0" (connect: 0.00s, validation: 0.00s, handshake: 0.09s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0-notifications" at "2025-08-17T22:51:54.4164454Z"
[Licensing::Module] Licensing Background thread has ended after 0.30s
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1051.17 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.8088
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56092
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.008323 seconds.
- Loaded All Assemblies, in  0.991 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.538 seconds
Domain Reload Profiling: 1510ms
	BeginReloadAssembly (247ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (110ms)
	LoadAllAssembliesAndSetupDomain (536ms)
		LoadAssemblies (227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (531ms)
			TypeCache.Refresh (529ms)
				TypeCache.ScanAssembly (510ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (538ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (469ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (87ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (183ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.800 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.24 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.881 seconds
Domain Reload Profiling: 5638ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (105ms)
	LoadAllAssembliesAndSetupDomain (1232ms)
		LoadAssemblies (985ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (457ms)
			TypeCache.Refresh (359ms)
				TypeCache.ScanAssembly (336ms)
			BuildScriptInfoCaches (73ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (3882ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3577ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (460ms)
			ProcessInitializeOnLoadAttributes (939ms)
			ProcessInitializeOnLoadMethodAttributes (2094ms)
			AfterProcessingInitializeOnLoad (75ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 23.22 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7977 unused Assets / (7.0 MB). Loaded Objects now: 8981.
Memory consumption went from 225.5 MB to 218.5 MB.
Total: 16.972700 ms (FindLiveObjects: 1.494200 ms CreateObjectMapping: 1.910400 ms MarkObjects: 9.130900 ms  DeleteObjects: 4.435900 ms)

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0