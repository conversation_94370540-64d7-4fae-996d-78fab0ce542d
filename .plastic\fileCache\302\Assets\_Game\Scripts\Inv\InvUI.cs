using UnityEngine;
using UnityEngine.UIElements;
using UnityEngine.EventSystems;
using System.Collections;
using System.Collections.Generic;
using Inventory;
using KinematicCharacterController.FPS;
using System.Linq;

public class InvUI : MonoBehaviour
{
    private EquipmentManager equipmentManager;
    private VisualElement root;
    private ScrollView invScrollView;
    private StyleSheet styleSheet;
    private VisualElement inventoryPanel;
    private CustomCursor customCursor;
    private bool isInventoryVisible = false;
    private InvDragAndDropManager dragAndDropManager;
    [SerializeField] private bool useCursorInfluenceWhenInventoryClosed = true;
    [SerializeField] private PlayerStatus playerStatus;
    private Label weightLabel;
    private Label energyLabel;
    private Label currencyLabel;
    private VisualElement batteryFill;
    [SerializeField] private UIPanelManager uiPanelManager;
    private VisualElement invPlaceholder;
    [SerializeField] private MoneyCounterAnimation moneyCounterAnimation;

    // References to FPS camera components
    private FPSCharacterCamera fpsCamera;
    private FPSPlayerManager playerManager;

    // Reference to event system
    private EventSystem eventSystem;

    // New property to track if we need to reset the scroll view
    private bool needsScrollReset = false;

    // Add this at the top of the class definition - after the private variables
	private float lastUIUpdateTime = 0f;
	private const float UI_UPDATE_THROTTLE = 0.1f; // Minimum time between full UI updates in seconds
	private Coroutine continuousUIUpdateCoroutine;

    public bool IsInventoryVisible() => isInventoryVisible;

    private void Start()
    {
        InitializeComponents();

        // Ensure playerStatus is assigned
        if (playerStatus == null)
        {
            playerStatus = FindObjectOfType<PlayerStatus>();
            if (playerStatus == null)
            {
                Debug.LogError("PlayerStatus not found. Currency display won't work properly.");
            }
        }

        SetupEventListeners();
        HideInventory();
		UpdateUI();

		// Ensure currency display is initialized immediately, not just when showing inventory
		if (playerStatus != null && currencyLabel != null)
		{
			if (moneyCounterAnimation != null)
			{
				moneyCounterAnimation.SetColoredCounterValue(currencyLabel, playerStatus.Currency);
			}
			Debug.Log($"InvUI: Initial currency set to {playerStatus.Currency}");
		}

        customCursor = FindObjectOfType<CustomCursor>();

        // Find FPS camera components
        fpsCamera = FindObjectOfType<FPSCharacterCamera>();
        playerManager = FindObjectOfType<FPSPlayerManager>();

        // Get EventSystem
        eventSystem = EventSystem.current;

        if (fpsCamera == null)
        {
            Debug.LogWarning("FPSCharacterCamera not found. Camera control will not be disabled when inventory is open.");
        }

        if (eventSystem == null)
        {
            Debug.LogWarning("EventSystem not found. UI input handling may not work properly.");
        }

		// Find UI elements
		weightLabel = root.Q<Label>(name: "WeightLabel");
		energyLabel = root.Q<Label>(name: "EnergyLabel");
		batteryFill = root.Q<VisualElement>(name: "BatteryFill");

        // Try to find by name first (more reliable), then fall back to class if needed
        var currencyLabels = root.Query<Label>(name: "CurrencyLabel").ToList();
        if (currencyLabels != null && currencyLabels.Count > 0)
        {
            currencyLabel = currencyLabels[0];
        }
		else
		{
			// Fallback to using class name
			var currencyByClass = root.Query<Label>(className: "header__money").ToList();
			if (currencyByClass != null && currencyByClass.Count > 0)
			{
				currencyLabel = currencyByClass[0];
				Debug.Log("Found currency label using class name fallback");
			}
		}

        // If MoneyCounterAnimation component not found, try to find it
        if (moneyCounterAnimation == null)
        {
            moneyCounterAnimation = GetComponent<MoneyCounterAnimation>();
            if (moneyCounterAnimation == null)
            {
                moneyCounterAnimation = FindObjectOfType<MoneyCounterAnimation>();
            }
        }

        // Hide inventory initially
        if (inventoryPanel != null)
        {
            inventoryPanel.style.display = DisplayStyle.None;
        }
    }

    private void InitializeComponents()
    {
        equipmentManager = GetComponent<EquipmentManager>();
        if (equipmentManager == null)
        {
            equipmentManager = FindObjectOfType<EquipmentManager>();
            if (equipmentManager == null)
            {
                Debug.LogError("EquipmentManager not found. Make sure it exists in the scene.");
                return;
            }
        }

        customCursor = GetComponent<CustomCursor>();
        dragAndDropManager = GetComponent<InvDragAndDropManager>();

        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument != null)
        {
            root = uiDocument.rootVisualElement;
            inventoryPanel = root.Q<VisualElement>("InvContainer");
            invScrollView = root.Q<ScrollView>("Inv");
            invPlaceholder = root.Q<VisualElement>("InvPlaceholder");

            if (invScrollView == null)
            {
                Debug.LogError("Failed to find Inv ScrollView in UI Document");
            }

            if (invPlaceholder == null)
            {
                Debug.LogWarning("InvPlaceholder not found in UI Document. Creating one at runtime.");
                // Create placeholder as a fallback if it doesn't exist in the UI document
                invPlaceholder = new VisualElement();
                invPlaceholder.name = "InvPlaceholder";
                invPlaceholder.AddToClassList("inventory-placeholder");

                var placeholderText = new Label();
                placeholderText.text = "Equip a bag to access inventory storage";
                placeholderText.AddToClassList("inventory-placeholder-text");

                invPlaceholder.Add(placeholderText);

                if (invScrollView != null)
                {
                    invScrollView.Add(invPlaceholder);
                }
            }

            // Initialize equipment slots with empty class
            var headSlot = root.Q<VisualElement>("HeadSlot");
            var chestSlot = root.Q<VisualElement>("ChestSlot");
            var bagSlot = root.Q<VisualElement>("BagSlot");

            if (headSlot != null)
            {
                headSlot.AddToClassList("equipment-slot");
                headSlot.AddToClassList("empty");
            }

            if (chestSlot != null)
            {
                chestSlot.AddToClassList("equipment-slot");
                chestSlot.AddToClassList("empty");
            }

            if (bagSlot != null)
            {
                bagSlot.AddToClassList("equipment-slot");
                bagSlot.AddToClassList("empty");
            }
        }
        else
        {
            Debug.LogError("UIDocument component not found on this GameObject");
        }
        if (uiPanelManager == null)
        {
            uiPanelManager = GetComponent<UIPanelManager>();
            if (uiPanelManager == null)
            {
                Debug.LogError("UIPanelManager not found. Please add it to the same GameObject as InvUI.");
            }
        }

        // Load and apply the stylesheet
        styleSheet = Resources.Load<StyleSheet>("inve");
        if (styleSheet != null && root != null)
        {
            root.styleSheets.Add(styleSheet);
        }
        else
        {
			Debug.LogError("Failed to load inventory stylesheet 'inve'. Make sure it exists in Resources folder.");
        }

		// Apply styles to specific elements
		if (root != null)
		{
			var equipmentSlots = root.Query<VisualElement>(className: "equipment-slot").ToList();
			foreach (var slot in equipmentSlots)
			{
				// Ensure these have the right class
				slot.AddToClassList("equipment-slot");
			}
		}
    }

    public VisualElement GetRootElement()
    {
        return root;
    }

    private void SetupEventListeners()
    {
        if (equipmentManager != null)
        {
            equipmentManager.OnEquipmentChanged += UpdateEquipmentSlot;
        }
		if (playerStatus != null)
		{
			playerStatus.OnEnergyChanged += OnEnergyChanged;
			playerStatus.OnHealthChanged += OnHealthChanged;
			playerStatus.OnCurrencyChanged += UpdateCurrencyDisplay;
		}
    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.Tab))
        {
            ToggleInventory();
        }

        // Prevent cursor lock while inventory is open
        if (isInventoryVisible && UnityEngine.Cursor.lockState == CursorLockMode.Locked)
        {
            UnityEngine.Cursor.lockState = CursorLockMode.None;
            UnityEngine.Cursor.visible = true;
        }
    }

    private void LateUpdate()
    {
        // Additional safety check to ensure cursor stays visible in inventory
        if (isInventoryVisible)
        {
            // Keep cursor visible and unlocked
            UnityEngine.Cursor.lockState = CursorLockMode.None;
            UnityEngine.Cursor.visible = true;
        }
    }

    private void ToggleInventory()
    {
        isInventoryVisible = !isInventoryVisible;
        if (isInventoryVisible)
        {
            ShowInventory();
        }
        else
        {
            CloseInventory();
        }
    }

    public void CloseInventory()
    {
        isInventoryVisible = false;
        HideInventory();

        // Find and update any interactable objects that might have local UI state
        BaseInteractable[] interactables = FindObjectsOfType<BaseInteractable>();
        foreach (var interactable in interactables)
        {
            if (interactable.IsUIOpen())
            {
                interactable.ForceCloseUI();
            }
        }

        // Close stash and shop panels
        if (uiPanelManager != null)
        {
            uiPanelManager.CloseStash();
            uiPanelManager.CloseShop();
        }
    }

    private void ShowInventory()
    {
        // Ensure currency is updated immediately when opening inventory
        if (playerStatus == null)
        {
            playerStatus = FindObjectOfType<PlayerStatus>();
        }

        if (playerStatus != null && currencyLabel != null)
        {
            if (moneyCounterAnimation != null)
            {
                moneyCounterAnimation.SetColoredCounterValue(currencyLabel, playerStatus.Currency);
            }
            else
            {
                currencyLabel.text = $"€ {playerStatus.Currency:000000000}";
            }        
        }

        if (inventoryPanel != null)
        {
            inventoryPanel.style.display = DisplayStyle.Flex;
        }

        UnityEngine.Cursor.visible = true;
        UnityEngine.Cursor.lockState = CursorLockMode.None;
        if (customCursor != null)
        {
            customCursor.SetInteractiveCursor();
        }

        if (fpsCamera != null)
        {
            fpsCamera.SetInventoryMode(true);
        }

        // If this is a reopen, we need special handling for the scroll view
        if (needsScrollReset)
        {
            StartCoroutine(ResetScrollViewAfterFrame());
        }

        UpdateUI();
		if (continuousUIUpdateCoroutine == null)
		{
			continuousUIUpdateCoroutine = StartCoroutine(ContinuousUIUpdate());
		}

        if (eventSystem != null)
        {
            eventSystem.SetSelectedGameObject(null);
        }

        // Notify the BatteryController that inventory has been opened
        NotifyBatteryController();
    }

    private IEnumerator ContinuousUIUpdate()
    {
        // Debug.Log("HOVER_DEBUG: Starting ContinuousUIUpdate coroutine");

        while (isInventoryVisible)
        {
            // Find and hover slots under the mouse cursor
            FindHoveredSlots();

            // Wait before the next update
            yield return new WaitForSeconds(0.1f);
        }

        // Debug.Log("HOVER_DEBUG: Stopping ContinuousUIUpdate coroutine");
    }

    public void HideInventory()
    {
        if (inventoryPanel != null)
        {
            inventoryPanel.style.display = DisplayStyle.None;
        }

        if (fpsCamera != null)
        {
            fpsCamera.SetInventoryMode(false);
        }

        // Set flag to reset scroll view on next open
        needsScrollReset = true;

		if (useCursorInfluenceWhenInventoryClosed)
        {
            UnityEngine.Cursor.visible = true;
            UnityEngine.Cursor.lockState = CursorLockMode.Confined;
        }
        else
        {
            // Force cursor to be invisible and locked when inventory is closed
            UnityEngine.Cursor.visible = false;
            UnityEngine.Cursor.lockState = CursorLockMode.Locked;
        }

        if (customCursor != null)
        {
            customCursor.SetDefaultCursor();
        }

		// Clear all hover states when closing inventory
		if (dragAndDropManager == null)
		{
			dragAndDropManager = GetComponent<InvDragAndDropManager>() ?? FindObjectOfType<InvDragAndDropManager>();
		}
		if (dragAndDropManager != null)
		{
			dragAndDropManager.ClearAllHoverStates();
		}

		// Force hide any tooltips (call public helper directly)
		var tooltipSystem = GetComponent<InvTooltipSystem>() ?? FindObjectOfType<InvTooltipSystem>();
		if (tooltipSystem != null)
		{
			tooltipSystem.ForceHideTooltip();
		}

		StartCoroutine(ForceCursorLockAfterDelay());
		if (continuousUIUpdateCoroutine != null)
		{
			StopCoroutine(continuousUIUpdateCoroutine);
			continuousUIUpdateCoroutine = null;
		}
    }

    private IEnumerator ForceCursorLockAfterDelay()
    {
        // Wait a frame to ensure other systems have processed
        yield return null;

        if (!isInventoryVisible && !useCursorInfluenceWhenInventoryClosed)
        {
            UnityEngine.Cursor.visible = false;
            UnityEngine.Cursor.lockState = CursorLockMode.Locked;

            // Explicitly force camera to exit inventory mode
            if (fpsCamera != null)
            {
                fpsCamera.SetInventoryMode(false);
            }
        }
    }

    public void ShowInventoryExternal()
    {
        if (!isInventoryVisible)
        {
            isInventoryVisible = true;
            ShowInventory();
        }
    }

    public void UpdateUI()
    {
        if (Time.time < lastUIUpdateTime + UI_UPDATE_THROTTLE && !isInventoryVisible)
        {
            // Skip update if throttled and inventory is hidden
            return;
        }

        // Check for and re-acquire critical references if they're null
        if (root == null)
        {
            Debug.LogWarning("UI root is null during UpdateUI. Attempting to re-initialize.");
            var uiDocument = GetComponent<UIDocument>();
            if (uiDocument != null)
            {
                root = uiDocument.rootVisualElement;
                if (root == null)
                {
                    Debug.LogError("Failed to re-initialize UI root element. UI updates will be skipped.");
                    return;
                }
                else
                {
                    Debug.Log("Successfully re-initialized UI root element in UpdateUI.");
                    // Re-initialize other UI elements since root has been reset
                    invScrollView = root.Q<ScrollView>("Inv");
                    invPlaceholder = root.Q<VisualElement>("InvPlaceholder");
                    weightLabel = root.Q<Label>(name: "WeightLabel");
                    energyLabel = root.Q<Label>(name: "EnergyLabel");
                    batteryFill = root.Q<VisualElement>(name: "BatteryFill");
                    currencyLabel = root.Q<Label>(name: "CurrencyLabel");
                }
            }
            else
            {
                Debug.LogError("UIDocument component not found when trying to re-initialize in UpdateUI.");
                return;
            }
        }

        // Get the drag and drop manager if not already available
        if (dragAndDropManager == null)
        {
            dragAndDropManager = GetComponent<InvDragAndDropManager>();
            if (dragAndDropManager == null)
            {
                dragAndDropManager = FindObjectOfType<InvDragAndDropManager>();
                if (dragAndDropManager == null)
                {
                    Debug.LogWarning("Could not find InvDragAndDropManager during UpdateUI.");
                }
            }
        }

        // Get equipmentManager if not already available
        if (equipmentManager == null)
        {
            equipmentManager = GetComponent<EquipmentManager>();
            if (equipmentManager == null)
            {
                equipmentManager = FindObjectOfType<EquipmentManager>();
                if (equipmentManager == null)
                {
                    Debug.LogError("Could not find EquipmentManager during UpdateUI.");
                    return;
                }
            }
        }

        // Skip UI updates if currently dragging - this is critical
        if (dragAndDropManager != null && dragAndDropManager.IsDraggingItem)
        {
            // Only update essentials during dragging
            UpdateWeightDisplay();
            UpdateEnergyDisplay();
            UpdateCurrencyDisplay(playerStatus?.Currency ?? 0);

            return;
        }

        // Remember which slots are currently hovered to preserve hover states
        var hoveredSlots = FindHoveredSlots();

        // Normal update when not dragging
        UpdateEquipmentDisplay();
        UpdateStorageDisplay();
        UpdateWeightDisplay();
        UpdateEnergyDisplay();
        if (playerStatus != null)
        {
            UpdateCurrencyDisplay(playerStatus.Currency);
        }
        else
        {
            // Try to find playerStatus if it's null
            playerStatus = FindObjectOfType<PlayerStatus>();
            if (playerStatus != null)
            {
                UpdateCurrencyDisplay(playerStatus.Currency);
            }
        }

        // Restore hover states
        RestoreHoveredStates(hoveredSlots);

        // Explicitly restore any hover effects through the drag and drop manager
        if (dragAndDropManager != null)
        {
            dragAndDropManager.RestoreHoverStates();
        }

        // Record timestamp of this update
        lastUIUpdateTime = Time.time;
    }

    public void UpdateEquipmentDisplay()
    {
        if (equipmentManager == null)
        {
            // Try to find equipment manager when it's null
            equipmentManager = FindObjectOfType<EquipmentManager>();
            if (equipmentManager == null)
            {
                Debug.LogError("equipmentManager is null in UpdateEquipmentDisplay and couldn't be found");
                return;
            }
            else
            {
                Debug.Log("Re-acquired equipmentManager reference");
            }
        }

        // Update equipment slots visually
        var slots = equipmentManager.GetEquipmentSlot(EquipmentSlotType.HeadSlot);
        UpdateEquipmentSlot(slots?.equippedItem, EquipmentSlotType.HeadSlot);

        slots = equipmentManager.GetEquipmentSlot(EquipmentSlotType.ChestSlot);
        UpdateEquipmentSlot(slots?.equippedItem, EquipmentSlotType.ChestSlot);

        slots = equipmentManager.GetEquipmentSlot(EquipmentSlotType.BagSlot);
        UpdateEquipmentSlot(slots?.equippedItem, EquipmentSlotType.BagSlot);
    }

    private void UpdateEquipmentSlot(EquipmentBase equipment, EquipmentSlotType slotType)
    {
        // Check if root is null and try to re-initialize it
        if (root == null)
        {
            Debug.LogWarning("UI root is null. Attempting to re-initialize UI components.");
            var uiDocument = GetComponent<UIDocument>();
            if (uiDocument != null)
            {
                root = uiDocument.rootVisualElement;
                if (root == null)
                {
                    Debug.LogError("Failed to re-initialize UI root element. UI updates will fail.");
                    return;
                }
                Debug.Log("Successfully re-initialized UI root element.");
            }
            else
            {
                Debug.LogError("UIDocument component not found when trying to re-initialize root.");
                return;
            }
        }

        string slotID = slotType.ToString() + "Icon";
        Image slotIcon = root.Q<Image>(slotID);
        VisualElement parentSlot = slotIcon?.parent;
        if (parentSlot != null)
        {
            // Ensure the parent slot has the equipment-slot class
            parentSlot.AddToClassList("equipment-slot");

            // Check if dragAndDropManager is valid before using it
            if (dragAndDropManager != null)
            {
                dragAndDropManager.RegisterSlotForDragging(parentSlot, slotType.ToString());
            }
            else
            {
                // Try to find the dragAndDropManager if it's null
                dragAndDropManager = FindObjectOfType<InvDragAndDropManager>();
                if (dragAndDropManager != null)
                {
                    dragAndDropManager.RegisterSlotForDragging(parentSlot, slotType.ToString());
                }
            }

            if (equipment != null)
            {
                slotIcon.sprite = equipment.Icon;
                slotIcon.style.display = DisplayStyle.Flex;
                parentSlot.AddToClassList("filled");
                parentSlot.RemoveFromClassList("empty");
            }
            else
            {
                slotIcon.sprite = null;
                slotIcon.style.display = DisplayStyle.Flex;
                parentSlot.RemoveFromClassList("filled");
                parentSlot.AddToClassList("empty");
            }
            parentSlot.style.display = DisplayStyle.Flex;
            parentSlot.pickingMode = PickingMode.Position;
        }
        else
        {
            Debug.LogWarning($"Icon or parent slot with ID '{slotID}' not found.");
        }
    }

    public void UpdateStorageDisplay()
    {
        if (invScrollView == null) return;

        if (equipmentManager == null)
        {
            // Try to find equipment manager when it's null
            equipmentManager = FindObjectOfType<EquipmentManager>();
            if (equipmentManager == null)
            {
                Debug.LogError("equipmentManager is null in UpdateStorageDisplay and couldn't be found");
                return;
            }
            else
            {
                Debug.Log("Re-acquired equipmentManager reference in UpdateStorageDisplay");
            }
        }

        // Instead of clearing the entire scroll view, find and remove only grid elements
        var existingGrids = invScrollView.Query(className: "grid-container").ToList();
        foreach (var grid in existingGrids)
        {
            invScrollView.Remove(grid);
        }

        var storageSlot = equipmentManager.GetEquipmentSlot(EquipmentSlotType.BagSlot);
        if (storageSlot?.equippedItem != null && storageSlot.equippedItem is Bag bag)
        {
            // Hide placeholder when we have a bag
            if (invPlaceholder != null)
            {
                invPlaceholder.style.display = DisplayStyle.None;
            }

            // Create a container for the storage grid
            var storageContainer = new VisualElement();
            storageContainer.style.position = Position.Relative;
            storageContainer.AddToClassList("grid-container");
            invScrollView.Add(storageContainer);

            // If storageContainer doesn't exist, create it with right dimensions
            var (width, height) = GetStorageGridDimensions(storageSlot.equippedItem);
            if (storageSlot.storageContainer == null ||
                storageSlot.storageContainer.GridWidth != width ||
                storageSlot.storageContainer.GridHeight != height)
            {
                storageSlot.storageContainer = new InvItemContainer(width, height);
            }

            // Use the working version's approach - create grid with items
            var itemsGrid = GridRenderer.CreateGrid(
                storageSlot.storageContainer,
                $"ContainerSlot_{storageSlot.slotType}",
                equipmentManager,
                dragAndDropManager
            );

            if (itemsGrid != null)
            {
                storageContainer.Add(itemsGrid);
            }
        }
        else
        {
            // Show placeholder when no bag is equipped
            if (invPlaceholder != null)
            {
                invPlaceholder.style.display = DisplayStyle.Flex;
            }
        }
    }

    private (int width, int height) GetStorageGridDimensions(EquipmentBase equipment)
    {
        return equipment switch
        {
            Bag bag => (bag.GridWidth, bag.GridHeight),
            _ => (1, 1)
        };
    }

    private void UpdateWeightDisplay()
    {
        if (weightLabel != null && playerStatus != null)
        {
            string currentWeightStr = FormatValue(playerStatus.currentWeight);
            string capacityWeightStr = FormatValue(playerStatus.effectiveWeightCapacity);
            weightLabel.text = $"{currentWeightStr}/{capacityWeightStr}";
        }
    }

    private void UpdateEnergyDisplay()
    {
        if (energyLabel != null && playerStatus != null)
        {
            string currentEnergyStr = FormatValue(playerStatus.currentEnergy);
            string maxEnergyStr = FormatValue(playerStatus.maxEnergy);
            energyLabel.text = $"{currentEnergyStr}/{maxEnergyStr}";
        }

        // Update the battery fill to represent energy level
        if (batteryFill != null && playerStatus != null)
        {
            batteryFill.style.width = Length.Percent(playerStatus.EnergyPercentage * 100);
        }
    }

    private string FormatValue(float value)
    {
        return Mathf.RoundToInt(value).ToString();
    }

    private void OnEquipmentChanged(EquipmentBase equipment, EquipmentSlotType slotType)
    {
        UpdateEquipmentSlot(equipment, slotType);
        UpdateStorageDisplay();
    }

    private VisualElement CreateItemsGridContainer()
    {
        var grid = new VisualElement
        {
            style =
            {
                flexDirection = FlexDirection.Row,
                flexWrap = Wrap.Wrap,
                position = Position.Relative
            }
        };
        grid.AddToClassList("ItemsGridContainer");
        return grid;
    }

    private VisualElement CreateItemSlot(string slotType, int index, Item item = null, bool isRotated = false)
    {
        return InvItemUtils.CreateItemSlot(slotType, index, item, isRotated, equipmentManager, dragAndDropManager);
    }

    private void OnDestroy()
    {
        // Clean up event listeners to prevent memory leaks
        if (equipmentManager != null)
        {
            equipmentManager.OnEquipmentChanged -= UpdateEquipmentSlot;
        }

		if (playerStatus != null)
		{
			playerStatus.OnEnergyChanged -= OnEnergyChanged;
			playerStatus.OnHealthChanged -= OnHealthChanged;
			playerStatus.OnCurrencyChanged -= UpdateCurrencyDisplay;
		}
    }

    private void OnEnergyChanged(float oldValue, float newValue)
    {
        UpdateEnergyDisplay();
    }

    private void OnHealthChanged(float oldValue, float newValue)
    {
        UpdateUI();
    }

    // New method to reset scroll view after frame
    private IEnumerator ResetScrollViewAfterFrame()
    {
        yield return null; // Wait one frame

        // Reset the scroll position
        if (invScrollView != null)
        {
            invScrollView.scrollOffset = Vector2.zero;

            // Force layout recalculation
            invScrollView.style.display = DisplayStyle.None;
            yield return null;
            invScrollView.style.display = DisplayStyle.Flex;

            // Reset the scrollbar visibility
            invScrollView.verticalScrollerVisibility = ScrollerVisibility.Auto;
            invScrollView.horizontalScrollerVisibility = ScrollerVisibility.Hidden;
        }

        needsScrollReset = false;
    }

    private void ContinueConsumption()
    {
        if (playerStatus != null)
        {
            playerStatus.UpdateEnergy(playerStatus.currentEnergy);
        }
    }

    private void UpdateCurrencyDisplay(int amount)
    {
        if (currencyLabel != null)
        {
            // Skip animation during startup
            if (Time.timeSinceLevelLoad < 1f)
            {
                currencyLabel.text = $"€ {amount:000000000}";
                Debug.Log($"InvUI: Set currency to {amount} (no animation - startup)");
                return;
            }

            // Get current displayed value by parsing the label
            int currentValue = 0;
            if (!string.IsNullOrEmpty(currencyLabel.text))
            {
                string numberPart = currencyLabel.text.Replace("€", "").Trim();
                int.TryParse(numberPart, out currentValue);
            }

            // Use animated counter if available and values differ
            if (moneyCounterAnimation != null && currentValue != amount)
            {
                moneyCounterAnimation.AnimateMoneyCounter(currencyLabel, currentValue, amount);
                Debug.Log($"InvUI: Animating currency from {currentValue} to {amount}");
            }
            else if (currentValue != amount)
            {
                // Fallback to direct update - only log if value actually changed
                currencyLabel.text = $"€ {amount:000000000}";
                Debug.Log($"InvUI: Set currency to {amount} (direct update)");
            }
        }
    }

    // Notify BatteryController that inventory has been opened
    private void NotifyBatteryController()
    {
        BatteryController batteryController = GetComponent<BatteryController>();
        if (batteryController == null)
        {
            batteryController = FindObjectOfType<BatteryController>();
        }

        if (batteryController != null)
        {
            batteryController.OnInventoryOpened();
        }
    }

    // New helper methods to preserve hover states
    private List<VisualElement> FindHoveredSlots()
    {
        List<VisualElement> hoveredSlots = new List<VisualElement>();

        if (root == null || !isInventoryVisible)
        {
            return hoveredSlots;
        }

        // Find all slots with hover class
        var slotsWithHover = root.Query().Where(element =>
            element.ClassListContains("slot-hovered")).ToList();

        // Debug.Log($"HOVER_DEBUG: Found {slotsWithHover.Count} hovered slots before UI update");

        hoveredSlots.AddRange(slotsWithHover);
        return hoveredSlots;
    }

    private void RestoreHoveredStates(List<VisualElement> hoveredSlots)
    {
        if (hoveredSlots == null || hoveredSlots.Count == 0) return;

        // Debug.Log($"HOVER_DEBUG: Restoring hover state to {hoveredSlots.Count} slots after UI update");

        foreach (var slot in hoveredSlots)
        {
            if (slot != null)
            {
                slot.AddToClassList("slot-hovered");
                // Debug.Log($"HOVER_DEBUG: Restored hover to slot {slot.userData}");
            }
        }
    }
}