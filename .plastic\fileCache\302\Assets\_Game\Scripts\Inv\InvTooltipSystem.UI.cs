using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;
using Inventory;

public partial class InvTooltipSystem
{
    // Add description label field
    private Label descriptionLabel;
    
    // ---------------- Tooltip UI ----------------
    private void CreateTooltipUI()
    {
        tooltip = new VisualElement { name = "Tooltip" };
        tooltip.AddToClassList("tooltip-container");
        tooltip.pickingMode      = PickingMode.Ignore;
        tooltip.style.display    = DisplayStyle.None;
        tooltip.style.visibility = Visibility.Hidden; // start invisible
        tooltip.style.position   = Position.Absolute;

        // Fallback styling
        tooltip.style.backgroundColor  = new Color(0f, 0f, 0f, 0.9f);
        tooltip.style.borderTopWidth   = 1;
        tooltip.style.borderBottomWidth = 1;
        tooltip.style.borderLeftWidth  = 1;
        tooltip.style.borderRightWidth = 1;
        Color borderColor = new Color(0.3f, 0.3f, 0.3f, 1f);
        tooltip.style.borderTopColor    = borderColor;
        tooltip.style.borderBottomColor = borderColor;
        tooltip.style.borderLeftColor   = borderColor;
        tooltip.style.borderRightColor  = borderColor;
        tooltip.style.paddingTop    = 2;
        tooltip.style.paddingBottom = 2;
        tooltip.style.paddingLeft   = 4;
        tooltip.style.paddingRight  = 4;
        // Allow natural width & shrink each time; ensure no min-width constraint
        tooltip.style.maxWidth = StyleKeyword.Auto;
        tooltip.style.minWidth = 0;
        tooltip.style.width    = StyleKeyword.Auto;

        // Name field (hidden by default, used for naming)
        nameField = new TextField();
        nameField.AddToClassList("tooltip-name-field");
        nameField.RegisterCallback<FocusInEvent>(OnFocusIn);
        nameField.RegisterCallback<FocusOutEvent>(OnFocusOut);
        nameField.RegisterCallback<KeyDownEvent>(OnKeyDown);

        // Style input element
        nameField.schedule.Execute(() =>
        {
            var inputElement = nameField.Q(className: "unity-text-field__input");
            if (inputElement != null)
            {
                inputElement.style.backgroundColor = new Color(25f/255f, 25f/255f, 25f/255f, 1f);
                inputElement.style.color           = Color.white;
                inputElement.style.borderTopWidth  = 0;
                inputElement.style.borderBottomWidth = 0;
                inputElement.style.borderLeftWidth   = 0;
                inputElement.style.borderRightWidth  = 0;
            }
        }).ExecuteLater(1);

        // Details label (item name)
        detailsLabel = new Label();
        detailsLabel.AddToClassList("tooltip-details");
        detailsLabel.style.color     = Color.white;
        detailsLabel.style.fontSize  = 11;
        detailsLabel.style.whiteSpace = WhiteSpace.Normal;
        detailsLabel.style.marginBottom = 4;

        // Description label (new)
        descriptionLabel = new Label();
        descriptionLabel.AddToClassList("tooltip-description");
        descriptionLabel.style.color     = new Color(0.7f, 0.7f, 0.7f, 1f);
        descriptionLabel.style.fontSize  = 10;
        descriptionLabel.style.whiteSpace = WhiteSpace.Normal;
        descriptionLabel.style.marginTop = 4;
        descriptionLabel.style.display = DisplayStyle.None;

        tooltip.Add(nameField);
        tooltip.Add(detailsLabel);
        tooltip.Add(descriptionLabel);
        root.Add(tooltip);
    }

    // Helper to ensure tooltip width resets to content size each display
    private void ResetTooltipWidth() => tooltip.style.width = new StyleLength(StyleKeyword.Auto);

    // Public entry point called by slot hover logic
    public void ShowTooltip(string slotType, Vector2 mousePos, VisualElement _)
    {
        if (isTooltipPrevented || isContextVisible) return;
        var stack = slotHandler.GetStackFromSlot(slotType);
        if (stack == null || stack.Item == null) return;

        if (tooltipCoroutine != null) StopCoroutine(tooltipCoroutine);
        tooltipCoroutine = StartCoroutine(ShowTooltipDelayed(slotType, mousePos));
    }

    private IEnumerator ShowTooltipDelayed(string slotType, Vector2 mousePos)
    {
        yield return new WaitForSeconds(hoverDelay);
        ShowTooltipImmediate(slotType, mousePos);
    }

    private void ShowTooltipImmediate(string slotType, Vector2 mousePos)
    {
        var stack = slotHandler.GetStackFromSlot(slotType);
        if (stack == null || stack.Item == null) return;

        // Reset width to auto for each display so it will tighten to content
        tooltip.style.minWidth = 0;
        tooltip.style.width    = StyleKeyword.Auto;

        currentSlotType = slotType;
        currentStack    = stack;
        currentMousePos = mousePos;

        UpdateTooltipContent();

        tooltip.style.display    = DisplayStyle.Flex;
        tooltip.style.visibility = Visibility.Hidden; // wait for layout

        tooltip.schedule.Execute(() =>
        {
            PositionTooltip(currentMousePos);
            tooltip.style.visibility = Visibility.Visible;
        }).ExecuteLater(1);

        isTooltipVisible = true;
    }

    private void UpdateTooltipContent()
    {
        if (currentStack?.Item == null || detailsLabel == null || nameField == null || descriptionLabel == null)
        {
            if (detailsLabel != null) detailsLabel.text = "Invalid item";
            if (nameField != null) nameField.style.display = DisplayStyle.None;
            if (descriptionLabel != null) descriptionLabel.style.display = DisplayStyle.None;
            return;
        }

        // Demo mode: don't allow naming
        if (!namingEnabled)
        {
            nameField.style.display = DisplayStyle.None;
            string display = !string.IsNullOrEmpty(currentStack.Item.PlayerGivenName)
                ? currentStack.Item.PlayerGivenName
                : currentStack.Item.itemName;
            detailsLabel.text = display;
            detailsLabel.style.display = DisplayStyle.Flex;
            
            // Show description if available
            string effectiveDescription = currentStack.Item.GetEffectiveDescription();
            if (!string.IsNullOrEmpty(effectiveDescription))
            {
                descriptionLabel.text = effectiveDescription;
                descriptionLabel.style.display = DisplayStyle.Flex;
            }
            else
            {
                descriptionLabel.style.display = DisplayStyle.None;
            }
            return;
        }

        bool isUnnamed = !currentStack.Item.HasPlayerName;
        nameField.style.display = DisplayStyle.None; // never edit inside pure tooltip

        if (isUnnamed)
        {
            detailsLabel.text = "???";
            descriptionLabel.text = "Right click to name item";
            descriptionLabel.style.display = DisplayStyle.Flex;
        }
        else
        {
            detailsLabel.text = currentStack.Item.PlayerGivenName;
            
            // Show description if available
            string effectiveDescription = currentStack.Item.GetEffectiveDescription();
            if (!string.IsNullOrEmpty(effectiveDescription))
            {
                descriptionLabel.text = effectiveDescription;
                descriptionLabel.style.display = DisplayStyle.Flex;
            }
            else
            {
                descriptionLabel.style.display = DisplayStyle.None;
            }
        }
        
        detailsLabel.style.display = DisplayStyle.Flex;
    }

    private void PositionTooltip(Vector2 mousePos)
    {
        Vector2 pos = mousePos;
        pos.x += 10f; // horizontal offset (right of cursor)
        pos.y -= tooltip.layout.height + 10f; // move above cursor

        float w = tooltip.layout.width;
        float h = tooltip.layout.height;

        if (pos.x + w > root.layout.width)  pos.x = mousePos.x - w - 10f;
        if (pos.x < 0) pos.x = 0;

        if (pos.y < 0) pos.y = mousePos.y + 10f; // if above top, place below cursor
        if (pos.y + h > root.layout.height) pos.y = root.layout.height - h;

        tooltip.style.left = pos.x;
        tooltip.style.top  = pos.y;
    }

    public void HideTooltip()
    {
        if (tooltipCoroutine != null)
        {
            StopCoroutine(tooltipCoroutine);
            tooltipCoroutine = null;
        }

        if (tooltip != null)
        {
            tooltip.style.display    = DisplayStyle.None;
            tooltip.style.visibility = Visibility.Hidden;
        }

        isTooltipVisible = false;
        RestorePlayerInput();
        EnsureCursorState();
    }

    // ---------------- Naming helpers (tooltip or context) ---------------
    private void ShowNamingInTooltip(Vector2 mousePos)
    {
        if (!namingEnabled) return; // feature disabled
        if (!isTooltipVisible) ShowTooltipImmediate(currentSlotType, mousePos);
        UpdateTooltipForNaming();
        nameField.schedule.Execute(nameField.Focus).ExecuteLater(50);
    }

    private void UpdateTooltipForNaming()
    {
        if (currentStack?.Item == null) return;
        nameField.style.display = DisplayStyle.Flex;
        nameField.value         = currentStack.Item.PlayerGivenName ?? string.Empty;
        detailsLabel.text       = "Enter item name and press Enter to confirm, or Esc to cancel";
        detailsLabel.style.display = DisplayStyle.Flex;
        descriptionLabel.style.display = DisplayStyle.None;
    }

    private void SaveName(TextField field)
    {
        string newName = field.value.Trim();
        if (currentStack == null || string.IsNullOrEmpty(newName) || newName == currentStack.Item.PlayerGivenName) return;
        currentStack.Item.PlayerGivenName = newName;
        Item.SavePlayerNames();
    }

    // ---------------- TextField events ----------------
    private void OnFocusIn(FocusInEvent _) => DisablePlayerInput();

    private void OnFocusOut(FocusOutEvent _)
    {
        SaveName(nameField);
        UpdateTooltipContent();
        RestorePlayerInput();
    }

    private void OnKeyDown(KeyDownEvent evt)
    {
        if (evt.keyCode == KeyCode.Return || evt.keyCode == KeyCode.KeypadEnter)
        {
            SaveName(nameField);
            UpdateTooltipContent();
            nameField.Blur();
            evt.StopPropagation();
        }
        else if (evt.keyCode == KeyCode.Escape)
        {
            nameField.value = currentStack?.Item?.PlayerGivenName ?? string.Empty;
            UpdateTooltipContent();
            nameField.Blur();
            evt.StopPropagation();
        }
    }
}