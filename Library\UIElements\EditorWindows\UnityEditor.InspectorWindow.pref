%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12386, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: UnityEditor.dll::UnityEditor.UIElements.SerializableJsonDictionary
  m_Keys:
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-IgnoredColliders__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-IgnoredColliders__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-IgnoredColliders__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-equipmentSlots__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-equipmentSlots__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-equipmentSlots__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-subscribedChannels__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-subscribedChannels__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-subscribedChannels__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-spawnPoints__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-spawnPoints__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-spawnPoints__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-m_Scenes__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-m_Scenes__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-m_Scenes__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-m_configObjects__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-m_configObjects__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-m_configObjects__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__bp-scene-list-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__bp-scripting-defines-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__bp-build-settings-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__bp-player-settings-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__bp-graphics-settings-override-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__bp-quality-settings-override-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-candidateMenuCameras__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-candidateMenuCameras__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-candidateMenuCameras__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-itemInformationList__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-itemInformationList__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-itemInformationList__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  m_Values:
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
