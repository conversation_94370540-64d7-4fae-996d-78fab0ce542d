[Licensing::Module] Trying to connect to existing licensing client channel...
Built from '6000.2/respin/6000.2.0f1-517f89d850d1' branch; Version is '6000.2.0f1 (eed1c594c913) revision 15651269'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias" at "2025-08-17T18:07:36.9584411Z"
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-17T18:07:36Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.2.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker1.log
-srvPort
50817
-licensingIpc
LicenseClient-ilias
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [36856]  Target information:

Player connection [36856]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1673718491 [EditorId] 1673718491 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36856]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1673718491 [EditorId] 1673718491 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36856]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1673718491 [EditorId] 1673718491 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36856]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1673718491 [EditorId] 1673718491 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36856]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1673718491 [EditorId] 1673718491 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36856] Host joined multi-casting on [***********:54997]...
Player connection [36856] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 33088, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.1'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-ilias"
[Licensing::IpcConnector] LicenseClient-ilias channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0" at "2025-08-17T18:07:37.0282396Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 34452, path: "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              69d27b7ca23c4f2db638d30790e0798b
  Correlation Id:          07b42c3946518dbd4b3a31c74382467b
  External correlation Id: 1368537100681254113
  Machine Id:              /GaE4A2aXaDfTh/RbExLPR8H29M=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-ilias-6000.2.0" (connect: 0.00s, validation: 0.00s, handshake: 0.01s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0-notifications" at "2025-08-17T18:07:37.0460878Z"
[Licensing::Module] Licensing Background thread has ended after 0.09s
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 239.77 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.8088
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56964
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006370 seconds.
- Loaded All Assemblies, in  0.758 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.518 seconds
Domain Reload Profiling: 1265ms
	BeginReloadAssembly (268ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (77ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (301ms)
		LoadAssemblies (255ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (298ms)
			TypeCache.Refresh (296ms)
				TypeCache.ScanAssembly (279ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (518ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (456ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (68ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.490 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 15.54 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.806 seconds
Domain Reload Profiling: 3271ms
	BeginReloadAssembly (329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (1007ms)
		LoadAssemblies (783ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (392ms)
			TypeCache.Refresh (304ms)
				TypeCache.ScanAssembly (279ms)
			BuildScriptInfoCaches (68ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1806ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1601ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (233ms)
			ProcessInitializeOnLoadAttributes (637ms)
			ProcessInitializeOnLoadMethodAttributes (697ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 20.85 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (7.5 MB). Loaded Objects now: 8980.
Memory consumption went from 223.5 MB to 215.9 MB.
Total: 18.447500 ms (FindLiveObjects: 1.265100 ms CreateObjectMapping: 1.492000 ms MarkObjects: 10.415300 ms  DeleteObjects: 5.273800 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.086 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 16.70 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.315 seconds
Domain Reload Profiling: 5372ms
	BeginReloadAssembly (563ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (159ms)
	RebuildCommonClasses (556ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (2855ms)
		LoadAssemblies (2400ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (713ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (670ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1316ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1010ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (279ms)
			ProcessInitializeOnLoadAttributes (572ms)
			ProcessInitializeOnLoadMethodAttributes (133ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 21.70 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.4 MB). Loaded Objects now: 9005.
Memory consumption went from 232.2 MB to 225.8 MB.
Total: 12.333800 ms (FindLiveObjects: 1.124400 ms CreateObjectMapping: 1.412000 ms MarkObjects: 6.318100 ms  DeleteObjects: 3.469100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.618 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 18.67 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.669 seconds
Domain Reload Profiling: 3262ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1122ms)
		LoadAssemblies (945ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (316ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (279ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1670ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1310ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (346ms)
			ProcessInitializeOnLoadAttributes (763ms)
			ProcessInitializeOnLoadMethodAttributes (164ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 18.93 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.8 MB). Loaded Objects now: 9007.
Memory consumption went from 234.2 MB to 228.4 MB.
Total: 10.879000 ms (FindLiveObjects: 0.844700 ms CreateObjectMapping: 0.930300 ms MarkObjects: 6.102700 ms  DeleteObjects: 3.000000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 16.56 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7926 unused Assets / (5.0 MB). Loaded Objects now: 9008.
Memory consumption went from 234.1 MB to 229.1 MB.
Total: 14.881200 ms (FindLiveObjects: 1.188800 ms CreateObjectMapping: 1.589700 ms MarkObjects: 7.298800 ms  DeleteObjects: 4.802400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.726 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 18.33 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.197 seconds
Domain Reload Profiling: 2901ms
	BeginReloadAssembly (356ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (1219ms)
		LoadAssemblies (785ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (585ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (549ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1197ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (963ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (249ms)
			ProcessInitializeOnLoadAttributes (575ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 18.91 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7975 unused Assets / (6.3 MB). Loaded Objects now: 9010.
Memory consumption went from 234.3 MB to 228.0 MB.
Total: 13.218700 ms (FindLiveObjects: 1.187800 ms CreateObjectMapping: 1.553000 ms MarkObjects: 6.761100 ms  DeleteObjects: 3.715800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 17.25 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7926 unused Assets / (5.4 MB). Loaded Objects now: 9010.
Memory consumption went from 234.2 MB to 228.8 MB.
Total: 9.386400 ms (FindLiveObjects: 1.119800 ms CreateObjectMapping: 1.310100 ms MarkObjects: 4.631900 ms  DeleteObjects: 2.323800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 19.78 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7925 unused Assets / (6.4 MB). Loaded Objects now: 9009.
Memory consumption went from 234.2 MB to 227.9 MB.
Total: 113.109700 ms (FindLiveObjects: 13.546000 ms CreateObjectMapping: 14.378600 ms MarkObjects: 80.535300 ms  DeleteObjects: 4.648300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.938 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.03 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.342 seconds
Domain Reload Profiling: 3261ms
	BeginReloadAssembly (367ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (1428ms)
		LoadAssemblies (1221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (384ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (341ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1343ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (996ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (288ms)
			ProcessInitializeOnLoadAttributes (544ms)
			ProcessInitializeOnLoadMethodAttributes (132ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 16.20 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.8 MB). Loaded Objects now: 9011.
Memory consumption went from 234.4 MB to 228.6 MB.
Total: 11.515000 ms (FindLiveObjects: 1.078200 ms CreateObjectMapping: 0.564900 ms MarkObjects: 6.706600 ms  DeleteObjects: 3.164600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2369.623923 seconds.
  path: Assets/_Game/Resources/ItemsIcons/AxeName.png
  artifactKey: Guid(e313fba2d45416d43a83bb9ed7202f28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/AxeName.png using Guid(e313fba2d45416d43a83bb9ed7202f28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e0d6f26bd758244ffea8970abb031849') in 0.3527267 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_Game/Resources/ItemsIcons/silhouette-graplin.png
  artifactKey: Guid(2d73bd06f0676564d83da9d002350b2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/silhouette-graplin.png using Guid(2d73bd06f0676564d83da9d002350b2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9aa83f79dcb41220407552b3e6773a82') in 0.0334684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/_Game/Resources/ItemsIcons/silhouette-isometric.png
  artifactKey: Guid(2b801f78134bf354ba353b2a8b7d3807) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/silhouette-isometric.png using Guid(2b801f78134bf354ba353b2a8b7d3807) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '301515f3ff9863f3a4d530c5e9c32048') in 0.0416361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_Game/Scripts/Interface/Crosshair/Dot.png
  artifactKey: Guid(139ac3927a2200f46ad0444cf364748d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Crosshair/Dot.png using Guid(139ac3927a2200f46ad0444cf364748d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '99c5ac46c281d7a6ac689be8370211dc') in 0.0578557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/_Game/Resources/UI/BatteryIcon_15.png
  artifactKey: Guid(aa4d2af1945051641a8682170bc4fd1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/BatteryIcon_15.png using Guid(aa4d2af1945051641a8682170bc4fd1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '600466f8010563694aebe0baa5823bb3') in 0.2227753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/_Game/Resources/UI/BatteryIcon_100.png
  artifactKey: Guid(c18a9b19a9e1cf04ba64dd1f128643f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/BatteryIcon_100.png using Guid(c18a9b19a9e1cf04ba64dd1f128643f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '645d97cc491b408099e0950e9618c3fa') in 0.1095708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Resources/UI/Placeholder_Chest.png
  artifactKey: Guid(cfe99bfd62c310048889033c2aaf6c93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/UI/Placeholder_Chest.png using Guid(cfe99bfd62c310048889033c2aaf6c93) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '68e079321b2e90bf9428b4770a4d5828') in 0.0443551 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/_Game/Resources/ParticleCloudWhite.png
  artifactKey: Guid(342059a0ded056c4da50a6f095ec1300) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ParticleCloudWhite.png using Guid(342059a0ded056c4da50a6f095ec1300) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '86a221e2be2458969d78767d4bf2ebb5') in 0.0410241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_Game/Resources/smoke1.png
  artifactKey: Guid(30cb8561f0e008642af25c80a57e8e76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/smoke1.png using Guid(30cb8561f0e008642af25c80a57e8e76) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b2a0d22bebc27adca84dc9476236befb') in 0.0351413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Graphy - Ultimate Stats Monitor/Textures/Debugger_Logo_Dark.png
  artifactKey: Guid(c4f28d6a8d8f6ba41a7710f6e2368f80) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Graphy - Ultimate Stats Monitor/Textures/Debugger_Logo_Dark.png using Guid(c4f28d6a8d8f6ba41a7710f6e2368f80) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '92497206a196939d61423c849a3dc529') in 0.0314921 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.86 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7931 unused Assets / (6.0 MB). Loaded Objects now: 9038.
Memory consumption went from 240.0 MB to 234.1 MB.
Total: 20.835100 ms (FindLiveObjects: 1.172200 ms CreateObjectMapping: 0.660500 ms MarkObjects: 14.776700 ms  DeleteObjects: 4.224400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.667 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.40 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.267 seconds
Domain Reload Profiling: 4912ms
	BeginReloadAssembly (492ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (124ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (2020ms)
		LoadAssemblies (1616ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (567ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (536ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (2269ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1799ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (335ms)
			ProcessInitializeOnLoadAttributes (883ms)
			ProcessInitializeOnLoadMethodAttributes (197ms)
			AfterProcessingInitializeOnLoad (372ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (99ms)
Refreshing native plugins compatible for Editor in 108.09 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.4 MB). Loaded Objects now: 9033.
Memory consumption went from 239.8 MB to 234.3 MB.
Total: 22.605500 ms (FindLiveObjects: 2.466400 ms CreateObjectMapping: 4.391500 ms MarkObjects: 8.646500 ms  DeleteObjects: 7.099300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.674 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.71 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.577 seconds
Domain Reload Profiling: 4218ms
	BeginReloadAssembly (793ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (80ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (211ms)
	LoadAllAssembliesAndSetupDomain (1540ms)
		LoadAssemblies (1574ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (518ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (472ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1578ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1229ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (319ms)
			ProcessInitializeOnLoadAttributes (640ms)
			ProcessInitializeOnLoadMethodAttributes (224ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 23.37 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.5 MB). Loaded Objects now: 9035.
Memory consumption went from 239.8 MB to 233.4 MB.
Total: 15.190300 ms (FindLiveObjects: 1.345300 ms CreateObjectMapping: 1.164200 ms MarkObjects: 7.907800 ms  DeleteObjects: 4.771200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.976 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.59 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.274 seconds
Domain Reload Profiling: 4230ms
	BeginReloadAssembly (534ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (142ms)
	RebuildCommonClasses (92ms)
	RebuildNativeTypeToScriptingClass (61ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1205ms)
		LoadAssemblies (968ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (433ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (347ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (2275ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1869ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (529ms)
			ProcessInitializeOnLoadAttributes (1060ms)
			ProcessInitializeOnLoadMethodAttributes (227ms)
			AfterProcessingInitializeOnLoad (41ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (50ms)
Refreshing native plugins compatible for Editor in 29.97 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (4.5 MB). Loaded Objects now: 9037.
Memory consumption went from 239.9 MB to 235.4 MB.
Total: 19.040300 ms (FindLiveObjects: 1.688500 ms CreateObjectMapping: 1.804500 ms MarkObjects: 9.513500 ms  DeleteObjects: 6.031800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.027 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.41 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.498 seconds
Domain Reload Profiling: 3481ms
	BeginReloadAssembly (431ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (76ms)
	LoadAllAssembliesAndSetupDomain (1403ms)
		LoadAssemblies (1178ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (406ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (361ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1499ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1101ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (344ms)
			ProcessInitializeOnLoadAttributes (581ms)
			ProcessInitializeOnLoadMethodAttributes (140ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 19.77 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.5 MB). Loaded Objects now: 9039.
Memory consumption went from 240.0 MB to 233.6 MB.
Total: 13.019500 ms (FindLiveObjects: 0.770000 ms CreateObjectMapping: 1.063600 ms MarkObjects: 6.881400 ms  DeleteObjects: 4.302900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.679 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.40 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.889 seconds
Domain Reload Profiling: 3550ms
	BeginReloadAssembly (530ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (1010ms)
		LoadAssemblies (919ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (321ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (276ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1892ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1556ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (395ms)
			ProcessInitializeOnLoadAttributes (915ms)
			ProcessInitializeOnLoadMethodAttributes (199ms)
			AfterProcessingInitializeOnLoad (37ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 25.29 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (4.4 MB). Loaded Objects now: 9041.
Memory consumption went from 240.2 MB to 235.8 MB.
Total: 14.570900 ms (FindLiveObjects: 1.427900 ms CreateObjectMapping: 1.532200 ms MarkObjects: 8.276200 ms  DeleteObjects: 3.333200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.964 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.17 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.545 seconds
Domain Reload Profiling: 3482ms
	BeginReloadAssembly (459ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (1343ms)
		LoadAssemblies (1196ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (372ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (331ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1546ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1217ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (437ms)
			ProcessInitializeOnLoadAttributes (605ms)
			ProcessInitializeOnLoadMethodAttributes (141ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 23.25 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.9 MB). Loaded Objects now: 9043.
Memory consumption went from 240.3 MB to 234.4 MB.
Total: 16.781700 ms (FindLiveObjects: 0.874800 ms CreateObjectMapping: 1.676800 ms MarkObjects: 10.089400 ms  DeleteObjects: 4.139100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7930.216144 seconds.
  path: Assets/_Game/Scripts/Interface/Menu/StartMenuManager.cs
  artifactKey: Guid(69cff9d095923014696214f99a1513ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Menu/StartMenuManager.cs using Guid(69cff9d095923014696214f99a1513ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c0cb8554a2c762c244a65ef8168d40e8') in 0.2657388 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.748 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 31.34 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.738 seconds
Domain Reload Profiling: 4398ms
	BeginReloadAssembly (674ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (83ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (99ms)
	LoadAllAssembliesAndSetupDomain (1784ms)
		LoadAssemblies (1604ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (572ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (524ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1738ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1412ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (457ms)
			ProcessInitializeOnLoadAttributes (708ms)
			ProcessInitializeOnLoadMethodAttributes (201ms)
			AfterProcessingInitializeOnLoad (37ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 32.79 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.4 MB). Loaded Objects now: 9045.
Memory consumption went from 240.4 MB to 234.0 MB.
Total: 19.297000 ms (FindLiveObjects: 1.691100 ms CreateObjectMapping: 2.403300 ms MarkObjects: 8.488000 ms  DeleteObjects: 6.712900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.288 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.53 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.558 seconds
Domain Reload Profiling: 4794ms
	BeginReloadAssembly (461ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (129ms)
	LoadAllAssembliesAndSetupDomain (1573ms)
		LoadAssemblies (1394ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (413ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (366ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (2559ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2280ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (568ms)
			ProcessInitializeOnLoadAttributes (1322ms)
			ProcessInitializeOnLoadMethodAttributes (277ms)
			AfterProcessingInitializeOnLoad (103ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 36.29 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (7.0 MB). Loaded Objects now: 9047.
Memory consumption went from 240.5 MB to 233.4 MB.
Total: 24.964300 ms (FindLiveObjects: 2.075700 ms CreateObjectMapping: 2.053300 ms MarkObjects: 13.461500 ms  DeleteObjects: 7.372300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.002 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 99.65 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.449 seconds
Domain Reload Profiling: 3412ms
	BeginReloadAssembly (547ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (81ms)
	LoadAllAssembliesAndSetupDomain (1252ms)
		LoadAssemblies (1117ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (405ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (358ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1449ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1170ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (377ms)
			ProcessInitializeOnLoadAttributes (619ms)
			ProcessInitializeOnLoadMethodAttributes (139ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 19.85 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.4 MB). Loaded Objects now: 9049.
Memory consumption went from 240.5 MB to 234.1 MB.
Total: 13.336100 ms (FindLiveObjects: 1.098000 ms CreateObjectMapping: 1.465500 ms MarkObjects: 6.754700 ms  DeleteObjects: 4.016000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.876 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.40 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.084 seconds
Domain Reload Profiling: 3935ms
	BeginReloadAssembly (704ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1008ms)
		LoadAssemblies (1147ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (329ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (288ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (2085ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1796ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (43ms)
			BeforeProcessingInitializeOnLoad (621ms)
			ProcessInitializeOnLoadAttributes (850ms)
			ProcessInitializeOnLoadMethodAttributes (209ms)
			AfterProcessingInitializeOnLoad (70ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 56.37 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.5 MB). Loaded Objects now: 9051.
Memory consumption went from 240.6 MB to 235.2 MB.
Total: 14.570100 ms (FindLiveObjects: 1.405100 ms CreateObjectMapping: 1.085200 ms MarkObjects: 8.356800 ms  DeleteObjects: 3.721600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 442.72 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7925 unused Assets / (5.8 MB). Loaded Objects now: 9051.
Memory consumption went from 240.5 MB to 234.7 MB.
Total: 15.457300 ms (FindLiveObjects: 1.284000 ms CreateObjectMapping: 1.270100 ms MarkObjects: 9.253100 ms  DeleteObjects: 3.648900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.518 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 39.10 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.174 seconds
Domain Reload Profiling: 7640ms
	BeginReloadAssembly (864ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (222ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (98ms)
	LoadAllAssembliesAndSetupDomain (4258ms)
		LoadAssemblies (4047ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (854ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (811ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (2175ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1724ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (429ms)
			ProcessInitializeOnLoadAttributes (1048ms)
			ProcessInitializeOnLoadMethodAttributes (196ms)
			AfterProcessingInitializeOnLoad (36ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 26.78 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (5.7 MB). Loaded Objects now: 9053.
Memory consumption went from 240.7 MB to 235.1 MB.
Total: 12.764500 ms (FindLiveObjects: 0.837100 ms CreateObjectMapping: 0.801900 ms MarkObjects: 7.530900 ms  DeleteObjects: 3.593400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.602 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 16.61 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.180 seconds
Domain Reload Profiling: 2763ms
	BeginReloadAssembly (473ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (999ms)
		LoadAssemblies (877ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (323ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (13ms)
			BuildScriptInfoCaches (275ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1181ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (930ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (251ms)
			ProcessInitializeOnLoadAttributes (510ms)
			ProcessInitializeOnLoadMethodAttributes (120ms)
			AfterProcessingInitializeOnLoad (39ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 24.06 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.4 MB). Loaded Objects now: 9055.
Memory consumption went from 240.8 MB to 234.4 MB.
Total: 16.241400 ms (FindLiveObjects: 1.282900 ms CreateObjectMapping: 1.538800 ms MarkObjects: 9.113500 ms  DeleteObjects: 4.305100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.362 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.13 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.798 seconds
Domain Reload Profiling: 4112ms
	BeginReloadAssembly (610ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (79ms)
	LoadAllAssembliesAndSetupDomain (1533ms)
		LoadAssemblies (1478ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (401ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (362ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1798ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1454ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (22ms)
			BeforeProcessingInitializeOnLoad (571ms)
			ProcessInitializeOnLoadAttributes (690ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 20.74 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.3 MB). Loaded Objects now: 9057.
Memory consumption went from 240.9 MB to 234.7 MB.
Total: 12.599700 ms (FindLiveObjects: 1.275400 ms CreateObjectMapping: 1.407400 ms MarkObjects: 6.030700 ms  DeleteObjects: 3.885400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.162 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 43.51 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.547 seconds
Domain Reload Profiling: 3667ms
	BeginReloadAssembly (403ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (81ms)
	LoadAllAssembliesAndSetupDomain (1561ms)
		LoadAssemblies (1218ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (542ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (499ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1548ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1222ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (325ms)
			ProcessInitializeOnLoadAttributes (707ms)
			ProcessInitializeOnLoadMethodAttributes (143ms)
			AfterProcessingInitializeOnLoad (38ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (37ms)
Refreshing native plugins compatible for Editor in 53.49 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (7.2 MB). Loaded Objects now: 9059.
Memory consumption went from 241.0 MB to 233.8 MB.
Total: 22.195500 ms (FindLiveObjects: 1.390600 ms CreateObjectMapping: 1.174500 ms MarkObjects: 14.301800 ms  DeleteObjects: 5.327000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.229 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 20.48 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.364 seconds
Domain Reload Profiling: 4573ms
	BeginReloadAssembly (1931ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (137ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (1097ms)
		LoadAssemblies (1269ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (374ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (330ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1364ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1106ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (289ms)
			ProcessInitializeOnLoadAttributes (569ms)
			ProcessInitializeOnLoadMethodAttributes (207ms)
			AfterProcessingInitializeOnLoad (34ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 24.05 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (6.4 MB). Loaded Objects now: 9061.
Memory consumption went from 241.1 MB to 234.8 MB.
Total: 16.699400 ms (FindLiveObjects: 1.304900 ms CreateObjectMapping: 1.783800 ms MarkObjects: 8.510800 ms  DeleteObjects: 5.098600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.837 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.02 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.334 seconds
Domain Reload Profiling: 5139ms
	BeginReloadAssembly (610ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (90ms)
	RebuildNativeTypeToScriptingClass (44ms)
	initialDomainReloadingComplete (98ms)
	LoadAllAssembliesAndSetupDomain (1962ms)
		LoadAssemblies (1773ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (501ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (455ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (2334ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1846ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (709ms)
			ProcessInitializeOnLoadAttributes (644ms)
			ProcessInitializeOnLoadMethodAttributes (397ms)
			AfterProcessingInitializeOnLoad (72ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (50ms)
Refreshing native plugins compatible for Editor in 23.67 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7974 unused Assets / (10.1 MB). Loaded Objects now: 9063.
Memory consumption went from 241.2 MB to 231.1 MB.
Total: 28.744800 ms (FindLiveObjects: 1.709500 ms CreateObjectMapping: 1.408600 ms MarkObjects: 13.674000 ms  DeleteObjects: 11.951100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.381 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.24 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.760 seconds
Domain Reload Profiling: 4109ms
	BeginReloadAssembly (654ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (1565ms)
		LoadAssemblies (1422ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (424ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (10ms)
			BuildScriptInfoCaches (371ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1760ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1290ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (451ms)
			ProcessInitializeOnLoadAttributes (615ms)
			ProcessInitializeOnLoadMethodAttributes (187ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 21.85 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7975 unused Assets / (6.3 MB). Loaded Objects now: 9066.
Memory consumption went from 241.3 MB to 235.0 MB.
Total: 13.828400 ms (FindLiveObjects: 1.263900 ms CreateObjectMapping: 1.509300 ms MarkObjects: 6.945300 ms  DeleteObjects: 4.108600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7151.966891 seconds.
  path: Assets/_Game/Scenes/Main/Black.mat
  artifactKey: Guid(71183bf04f91bd84ca2f6e3d82c5d4d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Black.mat using Guid(71183bf04f91bd84ca2f6e3d82c5d4d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a5f03a379f6f7f9affe50946a562092') in 2.8305171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_Game/Scenes/Main/IndoorsSceneProfile.asset
  artifactKey: Guid(e21ba01aa32ab4d4299b42d6bb7ad35d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/IndoorsSceneProfile.asset using Guid(e21ba01aa32ab4d4299b42d6bb7ad35d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e1c2f425011eb7306633cb72764adfd5') in 0.0083676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/_Game/Scenes/Main/Ground.mat
  artifactKey: Guid(79b21e146fb4da848825e3737288c8ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Ground.mat using Guid(79b21e146fb4da848825e3737288c8ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '100f27df07ab359246d6fe3aae801536') in 0.0301153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Scenes/Main/Gold.mat
  artifactKey: Guid(ccd64db7074208049b6eefbaaf741e25) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Gold.mat using Guid(ccd64db7074208049b6eefbaaf741e25) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '625222f45f8cfdb241992d7fc26f66eb') in 0.0297948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-0_comp_dir.png
  artifactKey: Guid(3e63533a66a01ae4598e8d3c56e89431) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-0_comp_dir.png using Guid(3e63533a66a01ae4598e8d3c56e89431) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5d7e509d0dff796d19d6b128e8f4150a') in 0.04208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-2_comp_dir.png
  artifactKey: Guid(846a0c075bd5a8a4aacf52d00b4e11f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-2_comp_dir.png using Guid(846a0c075bd5a8a4aacf52d00b4e11f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '311ee378ef3c1df88f0ce5114f5fec72') in 0.0256996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/_Game/Scenes/Main/IndoorsSceneProfile 2.asset
  artifactKey: Guid(a437169795ab4fb44a701a8d6b60d2b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/IndoorsSceneProfile 2.asset using Guid(a437169795ab4fb44a701a8d6b60d2b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a498c90ecbe741dd330580edae39ce1d') in 0.0028004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-3_comp_dir.png
  artifactKey: Guid(a7f6b4a1a72e82442a944dc0f2c86e48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-3_comp_dir.png using Guid(a7f6b4a1a72e82442a944dc0f2c86e48) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd7d4bd4e2e680d92831d3302d54edd00') in 0.0201935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/_Game/Scripts/Interface/Menu/MenuCameraId.cs
  artifactKey: Guid(63044a86656d44742891744f99af5ae2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Menu/MenuCameraId.cs using Guid(63044a86656d44742891744f99af5ae2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61b42d72ded6be95df4d2bb9a44cc40d') in 0.0011444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_Game/Scenes/Main/DefaultVolume.preset
  artifactKey: Guid(cac0f50d7dba0f340b7a26ca7142ba1a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/DefaultVolume.preset using Guid(cac0f50d7dba0f340b7a26ca7142ba1a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cca881f4ffa93aeeee350bf5f2bd2811') in 0.0047934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/_Game/Scenes/Main/Lightmap-5_comp_dir.png
  artifactKey: Guid(34cf8157a793699478fde6856357723f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Lightmap-5_comp_dir.png using Guid(34cf8157a793699478fde6856357723f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c49c7856ea6a525b73f3278687abae2b') in 0.0697473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 28.03 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7927 unused Assets / (5.5 MB). Loaded Objects now: 9541.
Memory consumption went from 312.0 MB to 306.5 MB.
Total: 24.175000 ms (FindLiveObjects: 1.315400 ms CreateObjectMapping: 1.988500 ms MarkObjects: 15.976000 ms  DeleteObjects: 4.893500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Leak Detected : Persistent allocates 8 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.036 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 20.07 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.188 seconds
Domain Reload Profiling: 3166ms
	BeginReloadAssembly (534ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (67ms)
	LoadAllAssembliesAndSetupDomain (1296ms)
		LoadAssemblies (1136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (371ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (331ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1188ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (943ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (262ms)
			ProcessInitializeOnLoadAttributes (528ms)
			ProcessInitializeOnLoadMethodAttributes (123ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 20.15 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (5.7 MB). Loaded Objects now: 9176.
Memory consumption went from 246.5 MB to 240.9 MB.
Total: 11.738700 ms (FindLiveObjects: 1.121000 ms CreateObjectMapping: 1.016900 ms MarkObjects: 6.432400 ms  DeleteObjects: 3.167500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.993 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.48 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.234 seconds
Domain Reload Profiling: 3196ms
	BeginReloadAssembly (482ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (122ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (1339ms)
		LoadAssemblies (1098ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (447ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (404ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1235ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (960ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (274ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (130ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 18.68 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7976 unused Assets / (6.2 MB). Loaded Objects now: 9178.
Memory consumption went from 246.4 MB to 240.2 MB.
Total: 9.885500 ms (FindLiveObjects: 0.788500 ms CreateObjectMapping: 0.898300 ms MarkObjects: 5.130000 ms  DeleteObjects: 3.067800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2197.087659 seconds.
  path: Assets/_Game/Resources/download.jpg
  artifactKey: Guid(b2c0e73bf88180445851d2d664b19108) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/download.jpg using Guid(b2c0e73bf88180445851d2d664b19108) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d3c0dbd9325d041db5ceaa8bdb12169') in 0.1472493 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.578092 seconds.
  path: Assets/_Game/Resources/Items/Axe.asset
  artifactKey: Guid(e1676de4e51f20a44894b675258852cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/Axe.asset using Guid(e1676de4e51f20a44894b675258852cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa9cdb16f80c461fd04e619dcfd4656a') in 0.0123118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_Game/Resources/Items/AxeManual.asset
  artifactKey: Guid(1abe8ea745304c345a70a6ea6cff2775) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/AxeManual.asset using Guid(1abe8ea745304c345a70a6ea6cff2775) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fbd336c848c7fc3a18430cee34756173') in 0.0037129 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/_Game/Resources/Items/EnergyCell.asset
  artifactKey: Guid(1d1ce1a8d90ab34459699b30853e41ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/EnergyCell.asset using Guid(1d1ce1a8d90ab34459699b30853e41ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f627b0357003dd225a8105bd56a5165') in 0.0074931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_Game/Resources/Items/Graplin.asset
  artifactKey: Guid(068b7f46280b7694b905cd6b05bf8738) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/Graplin.asset using Guid(068b7f46280b7694b905cd6b05bf8738) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '11c5751d27fb5c16daa036826c027463') in 0.0065254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/_Game/Resources/Items/Bag.asset
  artifactKey: Guid(dcee9101254d4514fa4723aad209c0fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/Bag.asset using Guid(dcee9101254d4514fa4723aad209c0fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '11fa9f227d05c54946d5c76d2271cd06') in 0.0078337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0