using UnityEngine;
using System.Collections.Generic;
using KinematicCharacterController.FPS;

public class GrabInteraction : MonoBehaviour
{
    [<PERSON><PERSON>("Grab Settings")]
    [Tooltip("How fast grabbed objects move toward target position")]
    [SerializeField] private float grabMovementSpeed = 30f;
    [Tooltip("How fast grabbed objects rotate when using right-click")]
    [SerializeField] private float grabRotationSpeed = 200f;
    [Tooltip("How much distance changes when scrolling")]
    [SerializeField] private float scrollDistanceChange = 2f;
    [Tooltip("Minimum grab distance")]
    [SerializeField] private float minGrabDistance = 1f;
    [Tooltip("Maximum grab distance")]
    [SerializeField] private float maxGrabDistance = 2f;
    [Tooltip("Force applied when throwing objects")]
    [SerializeField] private float throwForce = 20f;
    [Tooltip("How long F must be held to switch to grab mode")]
    [SerializeField] private float grabHoldTime = 0.2f;
    [Tooltip("Maximum distance at which objects can be grabbed")]
    [SerializeField] private float maxGrabReachDistance = 5f;
    [<PERSON>lt<PERSON>("Axis to rotate around with mouse X input")]
    [SerializeField] private Vector3 rotateXAxis = new Vector3(0, 1, 0);
    [Tooltip("Axis to rotate around with mouse Y input")]
    [SerializeField] private Vector3 rotateYAxis = new Vector3(1, 0, 0);
    [Tooltip("Axis to rotate around with E/Q keys")]
    [SerializeField] private Vector3 rotateZAxis = new Vector3(0, 0, 1);
    [Tooltip("Rotation speed for Z-axis (E/Q keys)")]
    [SerializeField] private float zRotationSpeed = 200f;
    [Tooltip("Snap angle increment when holding shift")]
    [SerializeField] private float snapAngle = 45f;
    [Tooltip("Sensitivity for snapped rotation")]
    [SerializeField] private float snappedRotationSensitivity = 8f;
    [Tooltip("Speed of rotation lerping")]
    [SerializeField] private float rotationLerpSpeed = 15f;
    
    [Header("References")]
    [Tooltip("Reference to player camera - assign in inspector")]
    [SerializeField] private Camera playerCameraRef;
    
    // FPS Camera reference for coordination
    private FPSCharacterCamera fpsCamera;
    private FPSPlayerManager playerManager;
    private ToolSelectionManager toolSelectionManager;
    private FPSCharacterController playerController;
    
    // Grab system state variables
    private bool isGrabbing = false;
    private GameObject grabbedObject;
    private Rigidbody grabbedRigidbody;
    private float currentGrabDistance;
    private Vector3 grabRotation;
    private Vector3 lastPosition;
    
    [Header("Heavy Pull Settings")]
    [Tooltip("Maximum mass (kg) that can be grabbed normally. Heavier objects use pull system")]
    [SerializeField] private float normalGrabMassThreshold = 100f;
    [Tooltip("Spring strength pulling the heavy object's grabbed point toward the target point")]
    [SerializeField] private float heavyPullSpring = 300f;
    [Tooltip("Damping applied against point velocity to stabilize the pull")]
    [SerializeField] private float heavyPullDamping = 20f;
    [Tooltip("Maximum pull force applied at the contact point")]
    [SerializeField] private float heavyMaxForce = 8000f;
    [Tooltip("Maximum allowed distance from the anchor to the target point (soft cap)")]
    [SerializeField] private float heavyMaxLeashDistance = 3.0f;
    [Tooltip("Reference mass used to scale slowdown effect with mass")]
    [SerializeField] private float heavyReferenceMass = 50f;
    [Tooltip("Player slow multiplier at max stretch for very light objects (mass ~ 0)")]
    [SerializeField] private float heavyMinMultiplierLight = 0.6f;
    [Tooltip("Player slow multiplier at max stretch for heavy objects (mass >= reference)")]
    [SerializeField] private float heavyMinMultiplierHeavy = 0.2f;
    [Tooltip("Scale throw force based on object mass (lighter = throw farther)")]
    [SerializeField] private bool scaleThrowForceByMass = true;
    [Tooltip("Maximum throw force multiplier for very light objects")]
    [SerializeField] private float lightObjectThrowMultiplier = 2f;
    
    // Original values to restore
    private float originalDrag;
    private float originalAngularDrag;
    private bool originalUseGravity;
    private RigidbodyInterpolation originalInterpolation;
    private CollisionDetectionMode originalCollisionMode;
    
    // Layer management
    private int originalLayer;
    private int grabbedObjectLayer = 31; // Layer 31 for grabbed objects
    private int playerLayer;
    
    // Stack for iterative layer setting
    private Stack<Transform> layerSetStack = new Stack<Transform>(32); // Pre-allocate for performance
    
    // Camera reference
    private Camera playerCamera => playerCameraRef != null ? playerCameraRef : Camera.main;
    
    // Original camera state
    private bool wasZooming = false;
    private Vector3 originalCameraRotation;
    private bool cameraControlsDisabled = false;
    
    // Rotation state
    private Quaternion desiredRotation = Quaternion.identity;
    private Vector3 lockedRotation = Vector3.zero;
    private bool isSnapping = false;
    private Vector3 forward;
    private Vector3 up;
    private Vector3 right;
    
    // Camera-relative rotation state
    private Quaternion initialRotationOffset = Quaternion.identity;
    private bool hasCustomRotation = false;
    
    // Public properties
    public bool IsGrabbing => isGrabbing;
    public float GrabHoldTime => grabHoldTime;
    
    // Heavy-object specific state
    private bool isHeavyPulling = false;
    private HeavyObjectManager heavyObjectManager;
    private Vector3 heavyLocalAnchor;
    private bool isLightHeavyObject = false; // True when grabbing a HeavyObjectManager with mass < threshold
    
    private void Awake()
    {
        // Find player layer
        playerLayer = LayerMask.NameToLayer("Player");
        if (playerLayer == -1)
        {
            Debug.LogWarning("[GrabInteraction] 'Player' layer not found. Physics interactions may not work correctly.");
        }
        else
        {
            Debug.Log($"[GrabInteraction] Found Player layer at index {playerLayer}");
        }
        
        // Log the grabbed object layer
        Debug.Log($"[GrabInteraction] Using layer {grabbedObjectLayer} for grabbed objects");
        
        // If camera not assigned, try to get main camera
        if (playerCameraRef == null)
        {
            playerCameraRef = Camera.main;
            Debug.Log($"[GrabInteraction] Using Camera.main as default");
        }
        
        // Get FPS camera reference - try multiple ways to ensure we get it
        if (fpsCamera == null)
        {
            // First try from the assigned camera reference
            if (playerCameraRef != null)
            {
                fpsCamera = playerCameraRef.GetComponent<FPSCharacterCamera>();
            }
            
            // If still null, try finding it in the scene
            if (fpsCamera == null)
            {
                fpsCamera = FindObjectOfType<FPSCharacterCamera>();
            }
            
            if (fpsCamera == null)
            {
                Debug.LogError("[GrabInteraction] Could not find FPSCharacterCamera component!");
            }
            else
            {
                Debug.Log("[GrabInteraction] Found FPSCharacterCamera component");
            }
        }
        
        // Find player manager
        playerManager = FindObjectOfType<FPSPlayerManager>();
        if (playerManager == null)
        {
            Debug.LogWarning("[GrabInteraction] FPSPlayerManager not found");
        }
        
        // Find tool selection manager
        toolSelectionManager = FindObjectOfType<ToolSelectionManager>();
        if (toolSelectionManager == null)
        {
            Debug.LogWarning("[GrabInteraction] ToolSelectionManager not found");
        }
    }
    
    public void Initialize(Camera camera)
    {
        if (camera != null)
        {
            playerCameraRef = camera;
            Debug.Log($"[GrabInteraction] Initialized with camera {camera.name}");
            
            // Also get FPS camera reference
            if (fpsCamera == null)
            {
                fpsCamera = camera.GetComponent<FPSCharacterCamera>();
                if (fpsCamera == null)
                {
                    Debug.LogError("[GrabInteraction] FPSCharacterCamera component not found on camera");
                }
            }
        }
    }
    
    private void OnEnable()
    {
        // Make sure we have the camera reference
        if (fpsCamera == null)
        {
            fpsCamera = FindObjectOfType<FPSCharacterCamera>();
            if (fpsCamera == null)
            {
                Debug.LogError("[GrabInteraction] Could not find FPSCharacterCamera component!");
            }
        }
        
        if (playerController == null)
        {
            playerController = FindObjectOfType<FPSCharacterController>();
        }
    }
    
    private void Update()
    {
        // Only update if we're grabbing something
        if (isGrabbing)
        {
            UpdateGrabbedObject();
            
            // Handle throw input (left click)
            if (Input.GetMouseButtonDown(0))
            {
                ReleaseGrabbedObject(true); // Throw
            }
            
            // Handle release input (F key up)
            if (Input.GetKeyUp(KeyCode.F))
            {
                ReleaseGrabbedObject(false); // Just drop
            }
        }
        else if (isHeavyPulling)
        {
            UpdateHeavyPull();
            
            // Handle throw input (left click)
            if (Input.GetMouseButtonDown(0))
            {
                ReleaseGrabbedObject(true); // Throw
            }
            
            // Handle release input (F key up)
            if (Input.GetKeyUp(KeyCode.F))
            {
                ReleaseGrabbedObject(false); // Release
            }
        }
    }
    
    /// <summary>
    /// Attempts to grab an object the player is looking at
    /// </summary>
    public void AttemptGrab()
    {
        if (playerCamera == null)
        {
            Debug.LogError("[GrabInteraction] Player camera is null. Cannot grab.");
            return;
        }
        
        Debug.Log($"[GrabInteraction] Attempting to grab with camera {playerCamera.name}");
        
        Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
        RaycastHit hitInfo;

        // Use a layermask to ignore the Player layer when raycasting
        int layerMask = ~(1 << playerLayer);
        if (Physics.Raycast(ray, out hitInfo, maxGrabReachDistance, layerMask))
        {
            Debug.Log($"[GrabInteraction] Raycast hit: {hitInfo.collider.gameObject.name}");
            
            // Check if the object has a rigidbody
            Rigidbody rb = hitInfo.collider.GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = hitInfo.collider.GetComponentInParent<Rigidbody>();
            }
            
            if (rb != null)
            {
                Debug.Log($"[GrabInteraction] Found rigidbody: {rb.gameObject.name}");
                
                // Start grabbing
                grabbedObject = rb.gameObject; // Get the actual rigidbody's gameObject
                grabbedRigidbody = rb;
                
                // Check for heavy-object behavior
                heavyObjectManager = rb.GetComponentInParent<HeavyObjectManager>();
                
                // Determine if this should be normal grab or heavy pull
                bool useNormalGrab = true;
                
                if (heavyObjectManager != null)
                {
                    // Check if the object is light enough for normal grab
                    if (heavyObjectManager.IsLightEnoughForNormalGrab)
                    {
                        Debug.Log($"[GrabInteraction] Heavy object with mass {heavyObjectManager.Mass}kg - using normal grab");
                        isLightHeavyObject = true;
                        useNormalGrab = true;
                    }
                    else
                    {
                        Debug.Log($"[GrabInteraction] Heavy object with mass {heavyObjectManager.Mass}kg - using heavy pull");
                        useNormalGrab = false;
                    }
                }
                
                // Remove highlight FIRST to get the true original layer
                IInteractable interactable = grabbedObject.GetComponent<IInteractable>();
                if (interactable != null)
                {
                    interactable.RemoveHighlight();
                }
                
                // Store original values to restore later (after highlight is removed)
                originalDrag = grabbedRigidbody.linearDamping;
                originalAngularDrag = grabbedRigidbody.angularDamping;
                originalUseGravity = grabbedRigidbody.useGravity;
                originalInterpolation = grabbedRigidbody.interpolation;
                originalCollisionMode = grabbedRigidbody.collisionDetectionMode;
                originalLayer = grabbedObject.layer; // Now this captures the real original layer
                
                if (useNormalGrab)
                {
                    // Normal grab for light objects (including light HeavyObjectManager objects)
                    
                    // Always start at minGrabDistance instead of using hit distance
                    currentGrabDistance = minGrabDistance;
                    
                    grabRotation = grabbedObject.transform.rotation.eulerAngles;
                    lastPosition = grabbedObject.transform.position;
                    
                    // Store initial rotation offset between camera and object
                    initialRotationOffset = Quaternion.Inverse(playerCamera.transform.rotation) * grabbedObject.transform.rotation;
                    hasCustomRotation = false;
                    
                    // Setup physics for better control
                    grabbedRigidbody.interpolation = RigidbodyInterpolation.Interpolate;
                    grabbedRigidbody.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
                    grabbedRigidbody.useGravity = false;
                    grabbedRigidbody.linearDamping = 4f; // Lower drag for more responsive movement
                    grabbedRigidbody.angularDamping = 4f;
                    
                    // Change layer to grabbed object layer (31) - using optimized method
                    SetLayerIteratively(grabbedObject, grabbedObjectLayer);
                    Debug.Log($"[GrabInteraction] Changed {grabbedObject.name} to layer {grabbedObjectLayer}");
                    
                    // Disable tool selection scrolling while grabbing
                    if (toolSelectionManager != null)
                    {
                        toolSelectionManager.SetScrollEnabled(false);
                        Debug.Log("[GrabInteraction] Disabled tool selection scrolling");
                    }
                    
                    // Mark as grabbing
                    isGrabbing = true;
                    isHeavyPulling = false;
                    
                    // Set up camera for grab mode
                    if (fpsCamera != null)
                    {
                        // Store whether the camera was zooming
                        wasZooming = fpsCamera.IsCurrentlyZooming;
                        
                        // Force stop zooming if it was active
                        if (wasZooming)
                        {
                            // We can't directly call StopZoom as it's private, but we can
                            // temporarily disable input which will effectively stop zoom
                            fpsCamera.SetPauseState(true);
                            fpsCamera.SetPauseState(false);
                        }
                    }
                    
                    Debug.Log($"[GrabInteraction] Started grabbing {grabbedObject.name} (normal mode)");
                }
                else
                {
                    // Heavy pull for truly heavy objects
                    
                    // For heavy objects: record the exact hit point as the local anchor
                    heavyLocalAnchor = rb.transform.InverseTransformPoint(hitInfo.point);
                    // Start at the actual hit distance clamped to allowed range
                    currentGrabDistance = Mathf.Clamp(hitInfo.distance, minGrabDistance, maxGrabDistance);
                    
                    // Disable tool selection scrolling while pulling
                    if (toolSelectionManager != null)
                    {
                        toolSelectionManager.SetScrollEnabled(false);
                    }
                    
                    isHeavyPulling = true;
                    isGrabbing = false;
                    isLightHeavyObject = false;
                    
                    // Apply initial slowdown for heavy pulling
                    if (playerController != null)
                    {
                        playerController.SetExternalSpeedMultiplier(0.6f);
                    }
                    Debug.Log($"[GrabInteraction] Heavy pull started on {grabbedObject.name} at local anchor {heavyLocalAnchor}");
                }
            }
            else
            {
                Debug.Log($"[GrabInteraction] No rigidbody found on {hitInfo.collider.gameObject.name}");
            }
        }
        else
        {
            Debug.Log($"[GrabInteraction] Raycast hit nothing");
        }
    }
    
    /// <summary>
    /// Iteratively sets layer for an object and all its children (optimized version)
    /// </summary>
    private void SetLayerIteratively(GameObject obj, int layer)
    {
        // Clear the stack
        layerSetStack.Clear();
        
        // Start with the root object
        layerSetStack.Push(obj.transform);
        
        // Process all objects iteratively
        while (layerSetStack.Count > 0)
        {
            Transform current = layerSetStack.Pop();
            current.gameObject.layer = layer;
            
            // Add all children to the stack
            foreach (Transform child in current)
            {
                layerSetStack.Push(child);
            }
        }
    }
    
    /// <summary>
    /// Recursively sets layer for an object and all its children (fallback for complex cases)
    /// </summary>
    private void SetLayerRecursively(GameObject obj, int layer)
    {
        obj.layer = layer;
        
        foreach (Transform child in obj.transform)
        {
            SetLayerRecursively(child.gameObject, layer);
        }
    }
    
    /// <summary>
    /// Updates the position and rotation of the grabbed object
    /// </summary>
    private void UpdateGrabbedObject()
    {
        if (grabbedObject == null || grabbedRigidbody == null)
        {
            isGrabbing = false;
            return;
        }
        
        // Check for scroll wheel to adjust distance - FLIPPED DIRECTION
        float scrollInput = Input.GetAxis("Mouse ScrollWheel");
        if (scrollInput != 0)
        {
            // Flipped sign to reverse direction
            currentGrabDistance += scrollInput * scrollDistanceChange;
            currentGrabDistance = Mathf.Clamp(currentGrabDistance, minGrabDistance, maxGrabDistance);
        }
        
        // Calculate target position
        Vector3 targetPosition = playerCamera.transform.position + playerCamera.transform.forward * currentGrabDistance;
        
        // Use velocity-based movement for more responsive control
        Vector3 desiredVelocity = (targetPosition - grabbedObject.transform.position) * grabMovementSpeed;
        
        // Apply velocity directly for instant response, but still respect collisions
        grabbedRigidbody.linearVelocity = desiredVelocity;
        
        // Store current position for velocity calculation next frame
        lastPosition = grabbedObject.transform.position;
        
        // Flag to track if right-click is being held for rotation
        bool isRotatingObject = Input.GetMouseButton(1);
        
        // Check for rotation input (right click + mouse movement)
        if (isRotatingObject)
        {
            // Mark that we're using custom rotation now
            hasCustomRotation = true;
            
            if (!cameraControlsDisabled)
            {
                // Make sure we have a valid camera reference
                if (fpsCamera == null)
                {
                    fpsCamera = FindObjectOfType<FPSCharacterCamera>();
                }
                
                if (fpsCamera != null)
                {
                    // Store camera rotation
                    originalCameraRotation = fpsCamera.transform.eulerAngles;
                    desiredRotation = grabbedRigidbody.rotation;
                    
                    // Enable grab rotation mode
                    fpsCamera.SetGrabRotationMode(true);
                    
                    cameraControlsDisabled = true;
                    Debug.Log("[GrabInteraction] Entered grab rotation mode");
                }
                else
                {
                    Debug.LogError("[GrabInteraction] Cannot enter grab rotation mode - no camera reference!");
                }
            }
            
            float mouseX = Input.GetAxis("Mouse X");
            float mouseY = Input.GetAxis("Mouse Y");
            
            // Check if shift is being held for snap rotation
            isSnapping = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
            
            // Update rotation axes
            UpdateRotationAxes();
            
            if (isSnapping)
            {
                HandleSnapRotation(mouseX, mouseY);
            }
            else if (Mathf.Abs(mouseX) > 0.1f || Mathf.Abs(mouseY) > 0.1f)
            {
                // Normal smooth rotation
                Quaternion rotationX = Quaternion.AngleAxis(-mouseX * grabRotationSpeed * Time.deltaTime, up);
                Quaternion rotationY = Quaternion.AngleAxis(mouseY * grabRotationSpeed * Time.deltaTime, right);
                
                desiredRotation = rotationY * rotationX * desiredRotation;
            }
            
            // Additional rotation around Z axis using E and Q keys
            if (Input.GetKey(KeyCode.E))
            {
                Quaternion rotationZ = Quaternion.AngleAxis(zRotationSpeed * Time.deltaTime, forward);
                desiredRotation = rotationZ * desiredRotation;
            }
            else if (Input.GetKey(KeyCode.Q))
            {
                Quaternion rotationZ = Quaternion.AngleAxis(-zRotationSpeed * Time.deltaTime, forward);
                desiredRotation = rotationZ * desiredRotation;
            }
            
            // Apply rotation with smooth interpolation
            grabbedRigidbody.MoveRotation(Quaternion.Lerp(
                grabbedRigidbody.rotation,
                desiredRotation,
                Time.deltaTime * rotationLerpSpeed
            ));
        }
        else if (cameraControlsDisabled)
        {
            // Re-enable camera when right-click is released
            if (fpsCamera != null)
            {
                fpsCamera.SetGrabRotationMode(false);
            }
            
            cameraControlsDisabled = false;
            Debug.Log("[GrabInteraction] Exited grab rotation mode");
        }
        else if (!hasCustomRotation)
        {
            // If not manually rotating and no custom rotation has been applied,
            // maintain the initial rotation offset relative to camera
            Quaternion targetRotation = playerCamera.transform.rotation * initialRotationOffset;
            grabbedRigidbody.MoveRotation(Quaternion.Lerp(
                grabbedRigidbody.rotation,
                targetRotation,
                Time.deltaTime * rotationLerpSpeed
            ));
        }
    }
    
    /// <summary>
    /// Applies a spring-like pull at the stored anchor point for heavy objects
    /// </summary>
    private void UpdateHeavyPull()
    {
        if (grabbedObject == null || grabbedRigidbody == null)
        {
            isHeavyPulling = false;
            if (playerController != null)
            {
                playerController.SetExternalSpeedMultiplier(1f);
            }
            return;
        }
        
        // Scroll to adjust distance while pulling
        float scrollInput = Input.GetAxis("Mouse ScrollWheel");
        if (scrollInput != 0)
        {
            currentGrabDistance += scrollInput * scrollDistanceChange;
            currentGrabDistance = Mathf.Clamp(currentGrabDistance, minGrabDistance, maxGrabDistance);
        }
        
        // Target point in front of camera
        Vector3 targetPoint = playerCamera.transform.position + playerCamera.transform.forward * currentGrabDistance;
        
        // Current anchor point on the rigidbody
        Vector3 anchorWorld = grabbedRigidbody.transform.TransformPoint(heavyLocalAnchor);
        
        // Soft-cap: clamp the desired target to a leash radius around the anchor
        Vector3 toDesired = targetPoint - anchorWorld;
        float toDesiredMag = toDesired.magnitude;
        if (toDesiredMag > heavyMaxLeashDistance && toDesiredMag > 0.0001f)
        {
            targetPoint = anchorWorld + toDesired * (heavyMaxLeashDistance / toDesiredMag);
        }

        // Spring force toward clamped target minus damping of point velocity
        Vector3 displacement = targetPoint - anchorWorld;
        Vector3 pointVelocity = grabbedRigidbody.GetPointVelocity(anchorWorld);
        Vector3 force = (displacement * heavyPullSpring) - (pointVelocity * heavyPullDamping);
        
        // Clamp maximum force
        if (force.sqrMagnitude > heavyMaxForce * heavyMaxForce)
        {
            force = force.normalized * heavyMaxForce;
        }
        
        grabbedRigidbody.AddForceAtPosition(force, anchorWorld, ForceMode.Force);
        
        // Player slowdown scales with stretch and object mass
        if (playerController != null)
        {
            float stretch = Mathf.Clamp01(displacement.magnitude / Mathf.Max(heavyMaxLeashDistance, 0.001f));
            float massNorm = Mathf.Clamp01(grabbedRigidbody != null && heavyReferenceMass > 0f ? grabbedRigidbody.mass / heavyReferenceMass : 0f);
            float minAtMaxStretch = Mathf.Lerp(heavyMinMultiplierLight, heavyMinMultiplierHeavy, massNorm);
            // At zero stretch, no slowdown; at max stretch, reduce to minAtMaxStretch depending on mass
            float multiplier = Mathf.Lerp(1f, minAtMaxStretch, stretch);
            playerController.SetExternalSpeedMultiplier(multiplier);
        }
    }
    
    private void UpdateRotationAxes()
    {
        if (!isSnapping)
        {
            forward = playerCamera.transform.forward;
            right = playerCamera.transform.right;
            up = playerCamera.transform.up;
            return;
        }
        
        // When snapping, use the nearest cardinal directions
        var directions = new List<Vector3>
        {
            grabbedObject.transform.forward,
            -grabbedObject.transform.forward,
            grabbedObject.transform.up,
            -grabbedObject.transform.up,
            grabbedObject.transform.right,
            -grabbedObject.transform.right
        };
        
        // Find the closest directions to the camera's orientation
        up = GetClosestDirection(directions, playerCamera.transform.up);
        directions.Remove(up);
        directions.Remove(-up);
        
        forward = GetClosestDirection(directions, playerCamera.transform.forward);
        directions.Remove(forward);
        directions.Remove(-forward);
        
        right = GetClosestDirection(directions, playerCamera.transform.right);
    }
    
    private Vector3 GetClosestDirection(List<Vector3> directions, Vector3 reference)
    {
        float maxDot = -Mathf.Infinity;
        Vector3 closest = Vector3.zero;
        
        foreach (Vector3 dir in directions)
        {
            float dot = Vector3.Dot(reference, dir);
            if (dot > maxDot)
            {
                maxDot = dot;
                closest = dir;
            }
        }
        
        return closest;
    }
    
    private void HandleSnapRotation(float mouseX, float mouseY)
    {
        // Add mouse movement to accumulated rotation
        lockedRotation += new Vector3(-mouseX, mouseY, 0) * grabRotationSpeed * Time.deltaTime;
        
        // Check if enough rotation has accumulated to snap
        if (Mathf.Abs(lockedRotation.x) > snappedRotationSensitivity || 
            Mathf.Abs(lockedRotation.y) > snappedRotationSensitivity)
        {
            // Calculate snap rotation
            Quaternion snapRotation = Quaternion.identity;
            
            if (Mathf.Abs(lockedRotation.x) > Mathf.Abs(lockedRotation.y))
            {
                // Snap around vertical axis
                float angle = lockedRotation.x > 0 ? snapAngle : -snapAngle;
                snapRotation = Quaternion.AngleAxis(angle, up);
            }
            else
            {
                // Snap around horizontal axis
                float angle = lockedRotation.y > 0 ? snapAngle : -snapAngle;
                snapRotation = Quaternion.AngleAxis(angle, right);
            }
            
            // Apply snap rotation
            desiredRotation = snapRotation * desiredRotation;
            
            // Reset accumulated rotation
            lockedRotation = Vector3.zero;
            
            // Ensure final rotation is aligned to grid
            Vector3 snappedEuler = desiredRotation.eulerAngles;
            snappedEuler.x = Mathf.Round(snappedEuler.x / snapAngle) * snapAngle;
            snappedEuler.y = Mathf.Round(snappedEuler.y / snapAngle) * snapAngle;
            snappedEuler.z = Mathf.Round(snappedEuler.z / snapAngle) * snapAngle;
            desiredRotation = Quaternion.Euler(snappedEuler);
        }
    }
    
    /// <summary>
    /// Releases the currently grabbed object
    /// </summary>
    /// <param name="throwObject">Whether to throw the object or just release it</param>
    public void ReleaseGrabbedObject(bool throwObject)
    {
        if (grabbedObject == null || grabbedRigidbody == null)
        {
            isGrabbing = false;
            isHeavyPulling = false;
            return;
        }
        
        Debug.Log($"[GrabInteraction] Releasing {grabbedObject.name}, throwing: {throwObject}");
        
        // Restore camera state
        if (fpsCamera != null && cameraControlsDisabled)
        {
            // Exit grab rotation mode
            fpsCamera.SetGrabRotationMode(false);
            cameraControlsDisabled = false;
        }
        
        // Reset cursor control
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
        
        // Restore original layer using optimized method
        SetLayerIteratively(grabbedObject, originalLayer);
        Debug.Log($"[GrabInteraction] Restored {grabbedObject.name} to original layer {originalLayer}");
        
        // Restore original physics settings
        if (originalInterpolation != grabbedRigidbody.interpolation)
            grabbedRigidbody.interpolation = originalInterpolation;
        if (originalCollisionMode != grabbedRigidbody.collisionDetectionMode)
            grabbedRigidbody.collisionDetectionMode = originalCollisionMode;
        grabbedRigidbody.linearDamping = originalDrag;
        grabbedRigidbody.angularDamping = originalAngularDrag;
        grabbedRigidbody.useGravity = originalUseGravity;
        
        // Apply throwing force if requested
        if (throwObject)
        {
            // Calculate throw force based on object mass if enabled
            float finalThrowForce = throwForce;
            
            if (scaleThrowForceByMass && grabbedRigidbody != null)
            {
                float mass = grabbedRigidbody.mass;
                
                // Light objects (< 10kg) get bonus throw force
                // Heavy objects (> 100kg) get reduced throw force
                if (mass < 10f)
                {
                    // Very light objects can be thrown much farther
                    float lightMultiplier = Mathf.Lerp(lightObjectThrowMultiplier, 1f, mass / 10f);
                    finalThrowForce *= lightMultiplier;
                }
                else if (mass > normalGrabMassThreshold)
                {
                    // Heavy objects are harder to throw
                    float heavyMultiplier = Mathf.Lerp(1f, 0.3f, (mass - normalGrabMassThreshold) / normalGrabMassThreshold);
                    finalThrowForce *= heavyMultiplier;
                }
            }
            
            if (isHeavyPulling)
            {
                // Apply impulse at the anchor point for natural torque/throw
                Vector3 anchorWorld = grabbedRigidbody.transform.TransformPoint(heavyLocalAnchor);
                grabbedRigidbody.AddForceAtPosition(playerCamera.transform.forward * finalThrowForce, anchorWorld, ForceMode.Impulse);
            }
            else
            {
                // Apply a stronger throw force based on camera direction
                grabbedRigidbody.linearVelocity = playerCamera.transform.forward * finalThrowForce;
            }
            Debug.Log($"[GrabInteraction] Throwing with force: {finalThrowForce} (base: {throwForce}, mass: {grabbedRigidbody.mass}kg)");
        }
        
        // If it's an InvItemPickup, tell it not to be picked up immediately after being thrown
        InvItemPickup itemPickup = grabbedObject.GetComponent<InvItemPickup>();
        if (itemPickup != null)
        {
            itemPickup.SetTimeSinceThrown(0f);
        }
        
        // Re-enable tool selection scrolling
        if (toolSelectionManager != null)
        {
            toolSelectionManager.SetScrollEnabled(true);
            Debug.Log("[GrabInteraction] Re-enabled tool selection scrolling");
        }
        
        // Reset grab variables
        grabbedObject = null;
        grabbedRigidbody = null;
        isGrabbing = false;
        isHeavyPulling = false;
        isLightHeavyObject = false;
        heavyObjectManager = null;
        if (playerController != null)
        {
            playerController.SetExternalSpeedMultiplier(1f);
        }
        
        // Reset rotation tracking
        initialRotationOffset = Quaternion.identity;
        hasCustomRotation = false;
    }
}