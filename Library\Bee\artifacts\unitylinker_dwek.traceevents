{ "pid": 7968, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 7968, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 7968, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 7968, "tid": 1, "ts": 1755527399149832, "dur": 2815468, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 7968, "tid": 1, "ts": 1755527399151616, "dur": 308555, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527399415374, "dur": 44285, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527399502390, "dur": 14529, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527399518160, "dur": 137173, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527399655374, "dur": 2116802, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527401772199, "dur": 181687, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527401961565, "dur": 3546, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527401965302, "dur": 425, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527401975950, "dur": 2145, "ph": "X", "name": "", "args": {} },
{ "pid": 7968, "tid": 1, "ts": 1755527401974799, "dur": 3670, "ph": "X", "name": "Write chrome-trace events", "args": {} },
