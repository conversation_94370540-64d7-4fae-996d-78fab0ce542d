using System.Collections.Generic;
using UnityEngine;

public static class ItemNamingService
{
	private static Dictionary<string, string> playerNameDatabase = new Dictionary<string, string>();
	private static Dictionary<string, string> playerDescriptionDatabase = new Dictionary<string, string>();
	private static bool isLoaded;

	public static string GetItemKey(Item item)
	{
		if (item == null) return string.Empty;
		return $"{item.name}_{item.itemName}";
	}

	public static string GetPlayerGivenName(Item item)
	{
		EnsureLoaded();
		if (item == null) return string.Empty;
		string key = GetItemKey(item);
		return playerNameDatabase.TryGetValue(key, out string savedName) ? savedName : string.Empty;
	}

	public static void SetPlayerGivenName(Item item, string value)
	{
		EnsureLoaded();
		if (item == null) return;
		string key = GetItemKey(item);
		playerNameDatabase[key] = value ?? string.Empty;
	}

	public static string GetPlayerGivenDescription(Item item)
	{
		EnsureLoaded();
		if (item == null) return string.Empty;
		string key = GetItemKey(item);
		return playerDescriptionDatabase.TryGetValue(key, out string savedDesc) ? savedDesc : string.Empty;
	}

	public static void SetPlayerGivenDescription(Item item, string value)
	{
		EnsureLoaded();
		if (item == null) return;
		string key = GetItemKey(item);
		playerDescriptionDatabase[key] = value ?? string.Empty;
	}

	public static bool HasPlayerName(Item item)
	{
		return !string.IsNullOrEmpty(GetPlayerGivenName(item));
	}

	public static bool HasPlayerDescription(Item item)
	{
		return !string.IsNullOrEmpty(GetPlayerGivenDescription(item));
	}

	public static string GetEffectiveDescription(Item item)
	{
		string playerDesc = GetPlayerGivenDescription(item);
		return !string.IsNullOrEmpty(playerDesc) ? playerDesc : (item != null ? item.Description : string.Empty);
	}

	public static void Save()
	{
		// Use the SerializableDictionary in Item.cs to serialize dictionaries
		string namesJson = JsonUtility.ToJson(new SerializableDictionary<string, string>(playerNameDatabase));
		PlayerPrefs.SetString("ItemPlayerNames", namesJson);

		string descriptionsJson = JsonUtility.ToJson(new SerializableDictionary<string, string>(playerDescriptionDatabase));
		PlayerPrefs.SetString("ItemPlayerDescriptions", descriptionsJson);

		PlayerPrefs.Save();
	}

	public static void Load()
	{
		playerNameDatabase = new Dictionary<string, string>();
		playerDescriptionDatabase = new Dictionary<string, string>();

		if (PlayerPrefs.HasKey("ItemPlayerNames"))
		{
			string json = PlayerPrefs.GetString("ItemPlayerNames");
			var loaded = JsonUtility.FromJson<SerializableDictionary<string, string>>(json);
			if (loaded != null)
			{
				playerNameDatabase = loaded.ToDictionary();
			}
		}

		if (PlayerPrefs.HasKey("ItemPlayerDescriptions"))
		{
			string json = PlayerPrefs.GetString("ItemPlayerDescriptions");
			var loaded = JsonUtility.FromJson<SerializableDictionary<string, string>>(json);
			if (loaded != null)
			{
				playerDescriptionDatabase = loaded.ToDictionary();
			}
		}

		isLoaded = true;
	}

	private static void EnsureLoaded()
	{
		if (!isLoaded)
		{
			Load();
		}
	}
}


